#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查工具
检查文件筛选工具所需的所有依赖
"""

import sys
import os
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*50)
    print(f"📋 {title}")
    print("="*50)

def check_python():
    """检查Python环境"""
    print_header("Python环境检查")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"平台: {sys.platform}")
    
    if sys.version_info >= (3, 6):
        print("✅ Python版本符合要求 (>= 3.6)")
        return True
    else:
        print("❌ Python版本过低，需要3.6或更高版本")
        return False

def check_core_files():
    """检查核心文件"""
    print_header("核心文件检查")
    
    required_files = [
        "file_filter.py",
        "main.py", 
        "config.py",
        "gui_pyside6.py",
        "gui.py"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (缺失)")
            missing_files.append(file)
    
    return len(missing_files) == 0

def check_pyside6():
    """检查PySide6"""
    print_header("PySide6依赖检查")
    
    try:
        import PySide6
        print(f"✅ PySide6已安装，版本: {PySide6.__version__}")
        
        # 检查关键模块
        modules = [
            "PySide6.QtWidgets",
            "PySide6.QtCore", 
            "PySide6.QtGui"
        ]
        
        for module in modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError as e:
                print(f"❌ {module}: {e}")
                return False
        
        return True
        
    except ImportError:
        print("❌ PySide6未安装")
        print("安装命令: pip install PySide6")
        return False

def check_tkinter():
    """检查tkinter"""
    print_header("tkinter依赖检查")
    
    try:
        import tkinter as tk
        print(f"✅ tkinter已安装，版本: {tk.TkVersion}")
        
        # 尝试创建测试窗口
        try:
            root = tk.Tk()
            root.withdraw()
            root.destroy()
            print("✅ tkinter窗口测试成功")
            return True
        except Exception as e:
            print(f"❌ tkinter窗口测试失败: {e}")
            return False
            
    except ImportError:
        print("❌ tkinter未安装")
        if sys.platform.startswith('linux'):
            print("在Ubuntu/Debian上安装: sudo apt-get install python3-tk")
        return False

def check_optional_deps():
    """检查可选依赖"""
    print_header("可选依赖检查")
    
    optional_deps = {
        "PyYAML": "YAML配置文件支持",
        "pytest": "单元测试",
        "tqdm": "进度条显示"
    }
    
    for dep, desc in optional_deps.items():
        try:
            __import__(dep.lower() if dep == "PyYAML" else dep)
            print(f"✅ {dep} - {desc}")
        except ImportError:
            print(f"⚠️  {dep} - {desc} (可选，未安装)")

def test_import_core():
    """测试导入核心模块"""
    print_header("核心模块导入测试")
    
    modules = [
        "file_filter",
        "config"
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            return False
        except Exception as e:
            print(f"⚠️  {module}: {e}")
    
    return True

def test_gui_imports():
    """测试GUI模块导入"""
    print_header("GUI模块导入测试")
    
    # 测试PySide6 GUI
    try:
        import gui_pyside6
        print("✅ gui_pyside6模块导入成功")
    except ImportError as e:
        print(f"❌ gui_pyside6导入失败: {e}")
    except Exception as e:
        print(f"⚠️  gui_pyside6导入警告: {e}")
    
    # 测试tkinter GUI
    try:
        import gui
        print("✅ gui模块导入成功")
    except ImportError as e:
        print(f"❌ gui导入失败: {e}")
    except Exception as e:
        print(f"⚠️  gui导入警告: {e}")

def show_recommendations():
    """显示建议"""
    print_header("安装建议")
    
    print("🎯 推荐安装顺序:")
    print("1. pip install PySide6")
    print("2. pip install PyYAML (可选)")
    print("3. pip install pytest (开发用)")
    print("4. pip install tqdm (可选)")
    
    print("\n🔧 如果遇到问题:")
    print("- 使用虚拟环境: python -m venv venv")
    print("- 升级pip: python -m pip install --upgrade pip")
    print("- 使用用户安装: pip install --user PySide6")
    
    if sys.platform.startswith('linux'):
        print("\n🐧 Linux系统额外步骤:")
        print("- sudo apt-get update")
        print("- sudo apt-get install python3-tk")
        print("- sudo apt-get install python3-pip")

def main():
    """主函数"""
    print("🔍 文件筛选工具 - 依赖检查")
    print(f"当前目录: {os.getcwd()}")
    
    # 执行所有检查
    checks = [
        ("Python环境", check_python),
        ("核心文件", check_core_files),
        ("PySide6", check_pyside6),
        ("tkinter", check_tkinter),
        ("可选依赖", check_optional_deps),
        ("核心模块", test_import_core),
        ("GUI模块", test_gui_imports)
    ]
    
    results = {}
    for name, check_func in checks:
        try:
            results[name] = check_func()
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            results[name] = False
    
    # 显示总结
    print_header("检查总结")
    
    for name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    # 判断是否可以运行
    critical_checks = ["Python环境", "核心文件", "核心模块"]
    can_run_cli = all(results.get(check, False) for check in critical_checks)
    can_run_pyside6 = can_run_cli and results.get("PySide6", False)
    can_run_tkinter = can_run_cli and results.get("tkinter", False)
    
    print(f"\n🎯 运行能力评估:")
    print(f"命令行版本: {'✅ 可以运行' if can_run_cli else '❌ 无法运行'}")
    print(f"PySide6界面: {'✅ 可以运行' if can_run_pyside6 else '❌ 无法运行'}")
    print(f"tkinter界面: {'✅ 可以运行' if can_run_tkinter else '❌ 无法运行'}")
    
    if not any([can_run_pyside6, can_run_tkinter]):
        show_recommendations()
    
    print(f"\n📝 建议:")
    if can_run_pyside6:
        print("✅ 推荐使用: python gui_pyside6.py")
    elif can_run_tkinter:
        print("✅ 可以使用: python gui.py")
    elif can_run_cli:
        print("✅ 可以使用命令行版本: python main.py --help")
    else:
        print("❌ 需要先解决依赖问题")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断检查")
    except Exception as e:
        print(f"\n❌ 检查过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
