#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题测试脚本
测试Windows主题和VS主题的显示效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QCheckBox, QLineEdit, QSpinBox, QLabel,
        QGroupBox, QTableWidget, QTableWidgetItem, QTextEdit
    )
    from PySide6.QtCore import QTimer
    from file_filter_tool.ui.themes import WindowsTheme, VSTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class ThemeTestWidget(QWidget):
    """主题测试控件"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "windows"
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("主题测试 - 文件筛选工具")
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.theme_btn = QPushButton("切换到 VS 主题")
        self.theme_btn.clicked.connect(self.switch_theme)
        theme_layout.addWidget(self.theme_btn)
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 测试控件组
        self.create_test_groups(layout)
    
    def create_test_groups(self, layout):
        """创建测试控件组"""
        
        # 输入控件组
        input_group = QGroupBox("输入控件测试")
        input_layout = QVBoxLayout(input_group)
        
        # 文本输入框
        input_layout.addWidget(QLabel("文本输入框:"))
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("请输入文本...")
        input_layout.addWidget(line_edit)
        
        # 数字输入框
        input_layout.addWidget(QLabel("数字输入框:"))
        spin_box = QSpinBox()
        spin_box.setRange(0, 100)
        spin_box.setValue(50)
        input_layout.addWidget(spin_box)
        
        layout.addWidget(input_group)
        
        # 选择控件组
        choice_group = QGroupBox("选择控件测试")
        choice_layout = QVBoxLayout(choice_group)
        
        # 复选框
        choice_layout.addWidget(QLabel("复选框测试:"))
        checkbox1 = QCheckBox("选项1 - 未选中")
        checkbox2 = QCheckBox("选项2 - 已选中")
        checkbox2.setChecked(True)
        checkbox3 = QCheckBox("选项3 - 禁用")
        checkbox3.setEnabled(False)
        
        choice_layout.addWidget(checkbox1)
        choice_layout.addWidget(checkbox2)
        choice_layout.addWidget(checkbox3)
        
        layout.addWidget(choice_group)
        
        # 按钮组
        button_group = QGroupBox("按钮测试")
        button_layout = QHBoxLayout(button_group)
        
        normal_btn = QPushButton("普通按钮")
        disabled_btn = QPushButton("禁用按钮")
        disabled_btn.setEnabled(False)
        
        button_layout.addWidget(normal_btn)
        button_layout.addWidget(disabled_btn)
        button_layout.addStretch()
        
        layout.addWidget(button_group)
        
        # 表格测试
        table_group = QGroupBox("表格测试")
        table_layout = QVBoxLayout(table_group)
        
        table = QTableWidget(3, 3)
        table.setHorizontalHeaderLabels(["列1", "列2", "列3"])
        
        # 填充测试数据
        for row in range(3):
            for col in range(3):
                item = QTableWidgetItem(f"数据 {row+1}-{col+1}")
                table.setItem(row, col, item)
        
        table_layout.addWidget(table)
        layout.addWidget(table_group)
        
        # 文本区域测试
        text_group = QGroupBox("文本区域测试")
        text_layout = QVBoxLayout(text_group)
        
        text_edit = QTextEdit()
        text_edit.setPlainText("这是一个文本编辑区域的测试内容。\n可以输入多行文本。\n测试主题效果。")
        text_edit.setMaximumHeight(100)
        
        text_layout.addWidget(text_edit)
        layout.addWidget(text_group)
    
    def apply_theme(self):
        """应用主题"""
        if self.current_theme == "windows":
            self.setStyleSheet(WindowsTheme.get_complete_style())
        else:
            self.setStyleSheet(VSTheme.get_complete_style())
    
    def switch_theme(self):
        """切换主题"""
        if self.current_theme == "windows":
            self.current_theme = "vs"
            self.theme_btn.setText("切换到 Windows 主题")
            print("🌙 切换到 Visual Studio 深色主题")
        else:
            self.current_theme = "windows"
            self.theme_btn.setText("切换到 VS 主题")
            print("☀️ 切换到 Windows 浅色主题")
        
        self.apply_theme()

def test_themes():
    """测试主题"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行主题测试")
        return False
    
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("主题测试")
    app.setApplicationVersion("1.0")
    
    print("🎨 主题测试启动")
    print("=" * 40)
    print("📋 测试内容:")
    print("  • Windows 渐变主题")
    print("  • Visual Studio 深色主题")
    print("  • 复选框 ✓ 符号显示")
    print("  • 各种控件的渐变效果")
    print("  • 悬停和焦点效果")
    print()
    print("💡 使用说明:")
    print("  • 点击 '切换主题' 按钮测试不同主题")
    print("  • 尝试悬停在各个控件上查看效果")
    print("  • 点击输入框查看焦点效果")
    print("  • 勾选复选框查看 ✓ 符号")
    print()
    
    # 创建测试窗口
    test_widget = ThemeTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行主题测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_themes()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
