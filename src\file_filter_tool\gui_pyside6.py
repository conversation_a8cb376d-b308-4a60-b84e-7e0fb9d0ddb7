
import sys
import os
import json
import platform
import subprocess
from pathlib import Path
from datetime import datetime

try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QGridLayout, QLabel, QLineEdit, QPushButton, QCheckBox, QComboBox,
        QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog,
        QMessageBox, QProgressBar, QStatusBar, QGroupBox, QSplitter,
        QTextEdit, QTabWidget, QSpinBox, QDoubleSpinBox, QFrame,
        QScrollArea, QListWidget, QListWidgetItem, QDialog, QDialogButtonBox
    )
    from PySide6.QtCore import Qt, QThread, Signal, QTimer, QSize
    from PySide6.QtGui import QIcon, QFont, QPixmap, QAction, QKeySequence
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    # 创建虚拟类以避免导入错误
    class QThread:
        pass
    class Signal:
        def __init__(self, *args):
            pass
    class QDialog:
        pass
    class QMainWindow:
        pass
    # 只在直接运行时显示错误信息
    if __name__ == '__main__':
        print("错误: 需要安装PySide6")
        print("请运行: pip install PySide6")
        sys.exit(1)

from .file_filter import FileFilter
from .config import ConfigManager, FilterConfig

class FileSearchThread(QThread):
    """文件搜索线程"""
    finished = Signal(list)
    progress = Signal(str)
    error = Signal(str)
    
    def __init__(self, file_filter, **kwargs):
        super().__init__()
        self.file_filter = file_filter
        self.search_params = kwargs
    
    def run(self):
        try:
            if self.search_params.get('find_duplicates', False):
                self.progress.emit("正在搜索文件...")
                # 先搜索所有文件
                temp_params = self.search_params.copy()
                temp_params['find_duplicates'] = False
                all_files = self.file_filter.find_files(**temp_params)

                self.progress.emit(f"正在检测重复文件... (共{len(all_files)}个文件)")
                # 再检测重复文件
                files = self.file_filter.find_files(**self.search_params)
            else:
                self.progress.emit("正在搜索文件...")
                files = self.file_filter.find_files(**self.search_params)

            self.finished.emit(files)
        except Exception as e:
            self.error.emit(str(e))

class FileOperationThread(QThread):
    """文件操作线程"""
    finished = Signal(str)
    progress = Signal(str)
    error = Signal(str)
    
    def __init__(self, file_filter, operation, files, destination=None):
        super().__init__()
        self.file_filter = file_filter
        self.operation = operation
        self.files = files
        self.destination = destination
    
    def run(self):
        try:
            if self.operation == "copy":
                self.progress.emit(f"正在复制 {len(self.files)} 个文件...")
                self.file_filter.copy_files(self.files, self.destination)
                self.finished.emit(f"成功复制 {len(self.files)} 个文件")
            elif self.operation == "move":
                self.progress.emit(f"正在移动 {len(self.files)} 个文件...")
                self.file_filter.move_files(self.files, self.destination)
                self.finished.emit(f"成功移动 {len(self.files)} 个文件")
            elif self.operation == "delete":
                self.progress.emit(f"正在删除 {len(self.files)} 个文件...")
                self.file_filter.delete_files(self.files, confirm=False)
                self.finished.emit(f"成功删除 {len(self.files)} 个文件")
        except Exception as e:
            self.error.emit(str(e))

class ConfigDialog(QDialog):
    """配置管理对话框"""
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setWindowTitle("配置管理")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()
        self.load_configs()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 配置列表
        self.config_list = QListWidget()
        layout.addWidget(QLabel("已保存的配置:"))
        layout.addWidget(self.config_list)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.load_btn = QPushButton("加载配置")
        self.delete_btn = QPushButton("删除配置")
        self.close_btn = QPushButton("关闭")
        
        button_layout.addWidget(self.load_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.load_btn.clicked.connect(self.load_selected_config)
        self.delete_btn.clicked.connect(self.delete_selected_config)
        self.close_btn.clicked.connect(self.accept)
        self.config_list.itemDoubleClicked.connect(self.load_selected_config)
    
    def load_configs(self):
        """加载配置列表"""
        self.config_list.clear()
        configs = self.config_manager.list_configs()
        for config_name in configs:
            self.config_list.addItem(config_name)
    
    def load_selected_config(self):
        """加载选中的配置"""
        current_item = self.config_list.currentItem()
        if current_item:
            config_name = current_item.text()
            config = self.config_manager.get_config(config_name)
            if config:
                self.selected_config = config
                self.accept()
    
    def delete_selected_config(self):
        """删除选中的配置"""
        current_item = self.config_list.currentItem()
        if current_item:
            config_name = current_item.text()
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除配置 '{config_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.config_manager.remove_config(config_name)
                self.load_configs()

class FileFilterMainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.file_filter = None
        self.config_manager = ConfigManager()
        self.found_files = []
        self.duplicate_groups = {}  # 存储重复文件分组信息
        self.similar_groups = {}    # 存储相似文件名分组信息
        self.search_thread = None
        self.operation_thread = None
        
        self.setWindowTitle("文件筛选过滤工具")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)

        # 设置窗口图标
        self.setup_icon()

        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()

        # 设置默认值
        self.path_edit.setText(".")
        self.recursive_check.setChecked(True)

    def setup_icon(self):
        """设置应用程序图标"""
        try:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent.parent

            # 尝试加载ICO图标（Windows首选）
            icon_path = project_root / "assets" / "res" / "logo.ico"
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                return

            # 如果ICO不存在，尝试PNG图标
            icon_path = project_root / "assets" / "res" / "logo.png"
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                return

            # 如果都不存在，使用系统默认图标
            print("警告: 未找到应用程序图标文件")

        except Exception as e:
            print(f"设置图标时出错: {e}")

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)
        
        # 上半部分：搜索条件
        search_widget = self.create_search_widget()
        splitter.addWidget(search_widget)
        
        # 下半部分：结果显示
        result_widget = self.create_result_widget()
        splitter.addWidget(result_widget)
        
        # 设置分割器比例
        splitter.setSizes([300, 400])
    
    def create_search_widget(self):
        """创建搜索条件部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 路径选择
        path_group = QGroupBox("搜索路径")
        path_layout = QHBoxLayout(path_group)
        
        self.path_edit = QLineEdit()
        self.path_browse_btn = QPushButton("浏览...")
        self.recursive_check = QCheckBox("递归搜索子目录")
        
        path_layout.addWidget(self.path_edit)
        path_layout.addWidget(self.path_browse_btn)
        path_layout.addWidget(self.recursive_check)
        
        layout.addWidget(path_group)
        
        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QGridLayout(filter_group)
        
        # 文件扩展名
        filter_layout.addWidget(QLabel("文件扩展名:"), 0, 0)
        self.extensions_edit = QLineEdit()
        self.extensions_edit.setPlaceholderText("如: .txt .py .jpg")
        filter_layout.addWidget(self.extensions_edit, 0, 1, 1, 2)
        
        # 文件名模式
        filter_layout.addWidget(QLabel("文件名模式:"), 1, 0)
        self.pattern_edit = QLineEdit()
        self.pattern_edit.setPlaceholderText("支持正则表达式")
        filter_layout.addWidget(self.pattern_edit, 1, 1, 1, 2)
        
        # 文件大小
        filter_layout.addWidget(QLabel("文件大小:"), 2, 0)
        size_widget = QWidget()
        size_layout = QHBoxLayout(size_widget)
        size_layout.setContentsMargins(0, 0, 0, 0)
        
        size_layout.addWidget(QLabel("最小:"))
        self.min_size_edit = QLineEdit()
        self.min_size_edit.setPlaceholderText("如: 1KB")
        size_layout.addWidget(self.min_size_edit)
        
        size_layout.addWidget(QLabel("最大:"))
        self.max_size_edit = QLineEdit()
        self.max_size_edit.setPlaceholderText("如: 100MB")
        size_layout.addWidget(self.max_size_edit)
        
        filter_layout.addWidget(size_widget, 2, 1, 1, 2)
        
        # 修改时间
        filter_layout.addWidget(QLabel("修改时间:"), 3, 0)
        time_widget = QWidget()
        time_layout = QHBoxLayout(time_widget)
        time_layout.setContentsMargins(0, 0, 0, 0)
        
        self.days_spin = QSpinBox()
        self.days_spin.setRange(0, 9999)
        self.days_spin.setValue(0)
        self.days_spin.setSpecialValueText("不限制")
        time_layout.addWidget(self.days_spin)
        time_layout.addWidget(QLabel("天内修改"))
        time_layout.addStretch()
        
        filter_layout.addWidget(time_widget, 3, 1, 1, 2)

        # 重复文件筛选
        filter_layout.addWidget(QLabel("重复文件:"), 4, 0)
        duplicate_widget = QWidget()
        duplicate_layout = QHBoxLayout(duplicate_widget)
        duplicate_layout.setContentsMargins(0, 0, 0, 0)

        self.duplicate_check = QCheckBox("仅显示重复文件")
        duplicate_layout.addWidget(self.duplicate_check)

        duplicate_layout.addWidget(QLabel("最少重复:"))
        self.min_duplicate_spin = QSpinBox()
        self.min_duplicate_spin.setRange(2, 999)
        self.min_duplicate_spin.setValue(2)
        self.min_duplicate_spin.setSpecialValueText("2个")
        self.min_duplicate_spin.setSuffix(" 个")
        duplicate_layout.addWidget(self.min_duplicate_spin)

        duplicate_layout.addStretch()

        filter_layout.addWidget(duplicate_widget, 4, 1, 1, 2)

        # 文件名相似度筛选
        filter_layout.addWidget(QLabel("文件名相似:"), 5, 0)
        similar_widget = QWidget()
        similar_layout = QHBoxLayout(similar_widget)
        similar_layout.setContentsMargins(0, 0, 0, 0)

        self.similar_check = QCheckBox("仅显示相似文件名")
        similar_layout.addWidget(self.similar_check)

        similar_layout.addWidget(QLabel("相同字符:"))
        self.min_common_chars_spin = QSpinBox()
        self.min_common_chars_spin.setRange(3, 50)
        self.min_common_chars_spin.setValue(5)
        self.min_common_chars_spin.setSuffix(" 个")
        similar_layout.addWidget(self.min_common_chars_spin)

        similar_layout.addWidget(QLabel("相似度:"))
        self.similarity_spin = QSpinBox()
        self.similarity_spin.setRange(50, 100)
        self.similarity_spin.setValue(70)
        self.similarity_spin.setSuffix(" %")
        similar_layout.addWidget(self.similarity_spin)

        similar_layout.addStretch()

        filter_layout.addWidget(similar_widget, 5, 1, 1, 2)

        layout.addWidget(filter_group)
        
        # 操作按钮
        button_group = QGroupBox("操作")
        button_layout = QHBoxLayout(button_group)
        
        self.search_btn = QPushButton("🔍 搜索文件")
        self.copy_btn = QPushButton("📋 复制到...")
        self.move_btn = QPushButton("📁 移动到...")
        self.delete_btn = QPushButton("🗑️ 删除文件")
        
        # 设置按钮样式
        for btn in [self.search_btn, self.copy_btn, self.move_btn, self.delete_btn]:
            btn.setMinimumHeight(35)
        
        self.search_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.delete_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_layout.addWidget(self.search_btn)
        button_layout.addWidget(self.copy_btn)
        button_layout.addWidget(self.move_btn)
        button_layout.addWidget(self.delete_btn)
        
        layout.addWidget(button_group)
        
        # 配置管理
        config_group = QGroupBox("配置管理")
        config_layout = QHBoxLayout(config_group)
        
        self.save_config_btn = QPushButton("💾 保存配置")
        self.load_config_btn = QPushButton("📂 加载配置")
        
        config_layout.addWidget(self.save_config_btn)
        config_layout.addWidget(self.load_config_btn)
        config_layout.addStretch()
        
        layout.addWidget(config_group)
        
        # 连接信号
        self.path_browse_btn.clicked.connect(self.browse_path)
        self.search_btn.clicked.connect(self.search_files)
        self.copy_btn.clicked.connect(self.copy_files)
        self.move_btn.clicked.connect(self.move_files)
        self.delete_btn.clicked.connect(self.delete_files)
        self.save_config_btn.clicked.connect(self.save_config)
        self.load_config_btn.clicked.connect(self.load_config)
        
        return widget
    
    def create_result_widget(self):
        """创建结果显示部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 结果标题
        result_label = QLabel("搜索结果")
        result_label.setFont(QFont("", 12, QFont.Weight.Bold))
        layout.addWidget(result_label)
        
        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(5)
        self.result_table.setHorizontalHeaderLabels(["文件名", "大小", "修改时间", "路径", "重复状态"])
        
        # 设置表格属性
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.result_table.setSortingEnabled(True)
        
        layout.addWidget(self.result_table)
        
        return widget
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 新建搜索
        new_search_action = QAction("新建搜索(&N)", self)
        new_search_action.setShortcut(QKeySequence.StandardKey.New)
        new_search_action.triggered.connect(self.new_search)
        file_menu.addAction(new_search_action)

        file_menu.addSeparator()

        # 导入配置
        import_config_action = QAction("导入配置(&I)...", self)
        import_config_action.triggered.connect(self.import_config)
        file_menu.addAction(import_config_action)

        # 导出配置
        export_config_action = QAction("导出配置(&E)...", self)
        export_config_action.triggered.connect(self.export_config)
        file_menu.addAction(export_config_action)

        file_menu.addSeparator()

        # 导出结果
        export_results_action = QAction("导出搜索结果(&R)...", self)
        export_results_action.triggered.connect(self.export_results)
        file_menu.addAction(export_results_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 清空搜索条件
        clear_filters_action = QAction("清空搜索条件(&C)", self)
        clear_filters_action.setShortcut(QKeySequence("Ctrl+R"))
        clear_filters_action.triggered.connect(self.clear_filters)
        tools_menu.addAction(clear_filters_action)

        tools_menu.addSeparator()

        # 打开目标文件夹
        open_folder_action = QAction("打开搜索目录(&O)", self)
        open_folder_action.triggered.connect(self.open_search_folder)
        tools_menu.addAction(open_folder_action)

        # 刷新搜索
        refresh_action = QAction("刷新搜索(&F)", self)
        refresh_action.setShortcut(QKeySequence.StandardKey.Refresh)
        refresh_action.triggered.connect(self.refresh_search)
        tools_menu.addAction(refresh_action)

        tools_menu.addSeparator()

        # 选项设置
        preferences_action = QAction("选项设置(&P)...", self)
        preferences_action.triggered.connect(self.show_preferences)
        tools_menu.addAction(preferences_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 使用指南
        help_action = QAction("使用指南(&H)", self)
        help_action.setShortcut(QKeySequence.StandardKey.HelpContents)
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)

        help_menu.addSeparator()

        # 检查更新
        update_action = QAction("检查更新(&U)", self)
        update_action.triggered.connect(self.check_updates)
        help_menu.addAction(update_action)

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        self.status_bar.showMessage("就绪")
    
    def browse_path(self):
        """浏览选择路径"""
        path = QFileDialog.getExistingDirectory(
            self, "选择搜索目录", self.path_edit.text()
        )
        if path:
            self.path_edit.setText(path)
    
    def parse_size(self, size_str):
        """解析大小字符串"""
        if not size_str.strip():
            return None
        
        size_str = size_str.upper().strip()
        multipliers = {'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
        
        for unit, multiplier in multipliers.items():
            if size_str.endswith(unit):
                try:
                    return int(float(size_str[:-len(unit)]) * multiplier)
                except ValueError:
                    return None
        
        try:
            return int(size_str)
        except ValueError:
            return None

    def search_files(self):
        """搜索文件"""
        if self.search_thread and self.search_thread.isRunning():
            return

        # 创建文件筛选器
        path = self.path_edit.text().strip() or "."
        if not Path(path).exists():
            QMessageBox.warning(self, "警告", f"路径不存在: {path}")
            return

        self.file_filter = FileFilter(path)

        # 解析参数
        extensions = None
        if self.extensions_edit.text().strip():
            extensions = [ext.strip() for ext in self.extensions_edit.text().split()]

        pattern = self.pattern_edit.text().strip() or None
        min_size = self.parse_size(self.min_size_edit.text())
        max_size = self.parse_size(self.max_size_edit.text())

        days = None
        if self.days_spin.value() > 0:
            days = self.days_spin.value()

        # 获取重复文件筛选参数
        find_duplicates = self.duplicate_check.isChecked()
        min_duplicate_count = self.min_duplicate_spin.value()

        # 获取文件名相似度筛选参数
        find_similar_names = self.similar_check.isChecked()
        min_common_chars = self.min_common_chars_spin.value()
        similarity_threshold = self.similarity_spin.value()

        # 创建搜索线程
        self.search_thread = FileSearchThread(
            self.file_filter,
            extensions=extensions,
            name_pattern=pattern,
            min_size=min_size,
            max_size=max_size,
            modified_days=days,
            recursive=self.recursive_check.isChecked(),
            find_duplicates=find_duplicates,
            min_duplicate_count=min_duplicate_count,
            find_similar_names=find_similar_names,
            min_common_chars=min_common_chars,
            similarity_threshold=similarity_threshold
        )

        # 连接信号
        self.search_thread.finished.connect(self.on_search_finished)
        self.search_thread.progress.connect(self.update_status)
        self.search_thread.error.connect(self.on_search_error)

        # 开始搜索
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.search_btn.setEnabled(False)
        self.search_thread.start()

    def on_search_finished(self, files):
        """搜索完成"""
        self.found_files = files

        # 如果启用了重复文件检测，获取分组信息
        if self.duplicate_check.isChecked() and files:
            self.duplicate_groups = self.file_filter.get_duplicate_groups(files)
        else:
            self.duplicate_groups = {}

        # 如果启用了文件名相似度检测，获取分组信息
        if self.similar_check.isChecked() and files:
            min_common_chars = self.min_common_chars_spin.value()
            similarity_threshold = self.similarity_spin.value()
            self.similar_groups = self.file_filter.get_similar_name_groups(
                files, min_common_chars, similarity_threshold)
        else:
            self.similar_groups = {}

        self.update_result_table()

        self.progress_bar.setVisible(False)
        self.search_btn.setEnabled(True)

        count = len(files)
        if self.duplicate_check.isChecked():
            group_count = len(self.duplicate_groups)
            self.status_bar.showMessage(f"找到 {count} 个重复文件，共 {group_count} 组")
        elif self.similar_check.isChecked():
            group_count = len(self.similar_groups)
            self.status_bar.showMessage(f"找到 {count} 个相似文件，共 {group_count} 组")
        else:
            self.status_bar.showMessage(f"找到 {count} 个文件")

        if count == 0:
            if self.duplicate_check.isChecked():
                QMessageBox.information(self, "提示", "没有找到重复文件")
            elif self.similar_check.isChecked():
                QMessageBox.information(self, "提示", "没有找到文件名相似的文件")
            else:
                QMessageBox.information(self, "提示", "没有找到符合条件的文件")

    def on_search_error(self, error_msg):
        """搜索错误"""
        self.progress_bar.setVisible(False)
        self.search_btn.setEnabled(True)
        self.status_bar.showMessage("搜索失败")
        QMessageBox.critical(self, "错误", f"搜索失败: {error_msg}")

    def update_result_table(self):
        """更新结果表格"""
        self.result_table.setRowCount(len(self.found_files))

        for row, file_path in enumerate(self.found_files):
            try:
                stat = file_path.stat()
                size = self.format_size(stat.st_size)
                modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')

                # 计算相对路径
                try:
                    relative_path = file_path.relative_to(Path(self.path_edit.text()))
                    parent_path = str(relative_path.parent) if str(relative_path.parent) != '.' else ''
                except ValueError:
                    parent_path = str(file_path.parent)

                # 检查是否为重复文件
                duplicate_status = self._get_duplicate_status(file_path)

                # 设置表格项
                self.result_table.setItem(row, 0, QTableWidgetItem(file_path.name))
                self.result_table.setItem(row, 1, QTableWidgetItem(size))
                self.result_table.setItem(row, 2, QTableWidgetItem(modified))
                self.result_table.setItem(row, 3, QTableWidgetItem(parent_path))
                self.result_table.setItem(row, 4, QTableWidgetItem(duplicate_status))

            except Exception:
                duplicate_status = self._get_duplicate_status(file_path)
                self.result_table.setItem(row, 0, QTableWidgetItem(file_path.name))
                self.result_table.setItem(row, 1, QTableWidgetItem("未知"))
                self.result_table.setItem(row, 2, QTableWidgetItem("未知"))
                self.result_table.setItem(row, 3, QTableWidgetItem(str(file_path.parent)))
                self.result_table.setItem(row, 4, QTableWidgetItem(duplicate_status))

    def _get_duplicate_status(self, file_path):
        """获取文件的重复/相似状态"""
        # 检查重复文件分组
        if self.duplicate_groups:
            for group_hash, file_list in self.duplicate_groups.items():
                if file_path in file_list:
                    group_size = len(file_list)
                    # 为每个组分配一个编号（基于哈希值的前6位）
                    group_id = group_hash[:6].upper()
                    return f"重复组 {group_id} ({group_size}个)"

        # 检查相似文件名分组
        if self.similar_groups:
            for group_key, file_list in self.similar_groups.items():
                if file_path in file_list:
                    group_size = len(file_list)
                    # 提取组编号
                    group_num = group_key.split('_')[-1]
                    return f"相似组 {group_num} ({group_size}个)"

        return "-"

    def format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

    def update_status(self, message):
        """更新状态"""
        self.status_bar.showMessage(message)

    def copy_files(self):
        """复制文件"""
        if not self.found_files:
            QMessageBox.warning(self, "警告", "请先搜索文件")
            return

        dest = QFileDialog.getExistingDirectory(self, "选择目标目录")
        if dest:
            self.perform_file_operation("copy", dest)

    def move_files(self):
        """移动文件"""
        if not self.found_files:
            QMessageBox.warning(self, "警告", "请先搜索文件")
            return

        dest = QFileDialog.getExistingDirectory(self, "选择目标目录")
        if dest:
            self.perform_file_operation("move", dest)

    def delete_files(self):
        """删除文件"""
        if not self.found_files:
            QMessageBox.warning(self, "警告", "请先搜索文件")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除 {len(self.found_files)} 个文件吗？\n\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.perform_file_operation("delete")

    def perform_file_operation(self, operation, destination=None):
        """执行文件操作"""
        if self.operation_thread and self.operation_thread.isRunning():
            return

        self.operation_thread = FileOperationThread(
            self.file_filter, operation, self.found_files, destination
        )

        # 连接信号
        self.operation_thread.finished.connect(self.on_operation_finished)
        self.operation_thread.progress.connect(self.update_status)
        self.operation_thread.error.connect(self.on_operation_error)

        # 开始操作
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.set_buttons_enabled(False)
        self.operation_thread.start()

    def on_operation_finished(self, message):
        """操作完成"""
        self.progress_bar.setVisible(False)
        self.set_buttons_enabled(True)
        self.status_bar.showMessage(message)
        QMessageBox.information(self, "成功", message)

        # 如果是移动或删除操作，重新搜索
        if "移动" in message or "删除" in message:
            self.search_files()

    def on_operation_error(self, error_msg):
        """操作错误"""
        self.progress_bar.setVisible(False)
        self.set_buttons_enabled(True)
        self.status_bar.showMessage("操作失败")
        QMessageBox.critical(self, "错误", f"操作失败: {error_msg}")

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.search_btn.setEnabled(enabled)
        self.copy_btn.setEnabled(enabled)
        self.move_btn.setEnabled(enabled)
        self.delete_btn.setEnabled(enabled)

    def save_config(self):
        """保存当前配置"""
        from PySide6.QtWidgets import QInputDialog

        name, ok = QInputDialog.getText(self, "保存配置", "请输入配置名称:")
        if ok and name.strip():
            config = FilterConfig(
                name=name.strip(),
                base_path=self.path_edit.text(),
                recursive=self.recursive_check.isChecked(),
                extensions=self.extensions_edit.text().split() if self.extensions_edit.text().strip() else None,
                name_pattern=self.pattern_edit.text() or None,
                min_size=self.parse_size(self.min_size_edit.text()),
                max_size=self.parse_size(self.max_size_edit.text()),
                modified_days=self.days_spin.value() if self.days_spin.value() > 0 else None
            )
            self.config_manager.add_config(config)
            QMessageBox.information(self, "成功", f"配置 '{name}' 已保存")

    def load_config(self):
        """加载配置"""
        dialog = ConfigDialog(self.config_manager, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if hasattr(dialog, 'selected_config'):
                config = dialog.selected_config
                self.load_config_values(config)
                QMessageBox.information(self, "成功", f"配置 '{config.name}' 已加载")

    def load_config_values(self, config):
        """加载配置值到界面"""
        self.path_edit.setText(config.base_path)
        self.recursive_check.setChecked(config.recursive)
        self.extensions_edit.setText(' '.join(config.extensions) if config.extensions else '')
        self.pattern_edit.setText(config.name_pattern or '')
        self.min_size_edit.setText(str(config.min_size) if config.min_size else '')
        self.max_size_edit.setText(str(config.max_size) if config.max_size else '')
        self.days_spin.setValue(config.modified_days if config.modified_days else 0)

    def new_search(self):
        """新建搜索 - 清空所有条件"""
        self.clear_filters()
        self.found_files = []
        self.duplicate_groups = {}
        self.similar_groups = {}
        self.update_result_table()
        self.status_bar.showMessage("已清空搜索条件")

    def clear_filters(self):
        """清空搜索条件"""
        self.path_edit.setText(".")
        self.recursive_check.setChecked(True)
        self.extensions_edit.clear()
        self.pattern_edit.clear()
        self.min_size_edit.clear()
        self.max_size_edit.clear()
        self.days_spin.setValue(0)
        self.duplicate_check.setChecked(False)
        self.min_duplicate_spin.setValue(2)
        self.similar_check.setChecked(False)
        self.min_common_chars_spin.setValue(5)
        self.similarity_spin.setValue(70)
        self.status_bar.showMessage("搜索条件已清空")

    def open_search_folder(self):
        """打开搜索目录"""
        path = self.path_edit.text().strip() or "."
        if Path(path).exists():
            try:
                import subprocess
                import platform

                if platform.system() == "Windows":
                    subprocess.run(["explorer", str(Path(path).resolve())])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", str(Path(path).resolve())])
                else:  # Linux
                    subprocess.run(["xdg-open", str(Path(path).resolve())])

                self.status_bar.showMessage(f"已打开目录: {path}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法打开目录: {e}")
        else:
            QMessageBox.warning(self, "警告", f"目录不存在: {path}")

    def refresh_search(self):
        """刷新搜索"""
        if self.found_files:
            self.search_files()
        else:
            QMessageBox.information(self, "提示", "请先执行搜索")

    def import_config(self):
        """导入配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    configs = json.load(f)

                # 导入所有配置
                count = 0
                for name, config_data in configs.items():
                    try:
                        config = FilterConfig(**config_data)
                        self.config_manager.add_config(config)
                        count += 1
                    except Exception as e:
                        print(f"导入配置 {name} 失败: {e}")

                QMessageBox.information(self, "成功", f"成功导入 {count} 个配置")
                self.status_bar.showMessage(f"已导入 {count} 个配置")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入配置失败: {e}")

    def export_config(self):
        """导出配置文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置文件", "config_export.json", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                configs = {}
                for name in self.config_manager.list_configs():
                    config = self.config_manager.get_config(name)
                    if config:
                        configs[name] = {
                            'name': config.name,
                            'base_path': config.base_path,
                            'recursive': config.recursive,
                            'extensions': config.extensions,
                            'name_pattern': config.name_pattern,
                            'min_size': config.min_size,
                            'max_size': config.max_size,
                            'modified_days': config.modified_days
                        }

                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(configs, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"配置已导出到: {file_path}")
                self.status_bar.showMessage("配置导出成功")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出配置失败: {e}")

    def export_results(self):
        """导出搜索结果"""
        if not self.found_files:
            QMessageBox.warning(self, "警告", "没有搜索结果可导出")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出搜索结果", "search_results.txt", "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("文件筛选结果\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"搜索路径: {self.path_edit.text()}\n")
                    f.write(f"找到文件: {len(self.found_files)} 个\n")
                    f.write("=" * 50 + "\n\n")

                    for file_path in self.found_files:
                        try:
                            stat = file_path.stat()
                            size = self.format_size(stat.st_size)
                            modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')
                            f.write(f"文件: {file_path.name}\n")
                            f.write(f"大小: {size}\n")
                            f.write(f"修改时间: {modified}\n")
                            f.write(f"路径: {file_path}\n")
                            f.write("-" * 30 + "\n")
                        except Exception:
                            f.write(f"文件: {file_path.name}\n")
                            f.write(f"路径: {file_path}\n")
                            f.write("-" * 30 + "\n")

                QMessageBox.information(self, "成功", f"搜索结果已导出到: {file_path}")
                self.status_bar.showMessage("搜索结果导出成功")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出结果失败: {e}")

    def show_preferences(self):
        """显示选项设置对话框"""
        QMessageBox.information(
            self, "选项设置",
            "选项设置功能正在开发中...\n\n当前版本支持的设置：\n"
            "• 通过菜单栏进行基本操作\n"
            "• 通过配置管理保存常用设置\n"
            "• 通过快捷键快速操作"
        )

    def show_help(self):
        """显示使用指南"""
        help_text = """
        <h2>文件筛选工具 - 使用指南</h2>

        <h3>🔍 基本搜索</h3>
        <p>1. 设置搜索路径：输入目录路径或点击"浏览"按钮</p>
        <p>2. 设置筛选条件：文件扩展名、名称模式、大小、修改时间</p>
        <p>3. 点击"搜索文件"按钮开始搜索</p>

        <h3>🔄 重复文件检测</h3>
        <p>1. 勾选"仅显示重复文件"复选框</p>
        <p>2. 设置"最少重复"数量（默认2个）</p>
        <p>3. 可结合其他条件精确筛选重复文件</p>
        <p>4. 结果表格会显示重复文件的分组信息</p>

        <h3>📁 文件操作</h3>
        <p>• <b>复制到</b>：将找到的文件复制到指定目录</p>
        <p>• <b>移动到</b>：将找到的文件移动到指定目录</p>
        <p>• <b>删除文件</b>：删除找到的文件（不可撤销）</p>

        <h3>⚙️ 配置管理</h3>
        <p>• <b>保存配置</b>：保存当前的筛选条件</p>
        <p>• <b>加载配置</b>：加载已保存的筛选条件</p>
        <p>• <b>导入/导出</b>：批量管理配置文件</p>

        <h3>⌨️ 快捷键</h3>
        <p>• <b>Ctrl+N</b>：新建搜索</p>
        <p>• <b>Ctrl+R</b>：清空搜索条件</p>
        <p>• <b>F5</b>：刷新搜索</p>
        <p>• <b>F1</b>：显示帮助</p>
        <p>• <b>Ctrl+Q</b>：退出程序</p>

        <h3>💡 使用技巧</h3>
        <p>• 使用正则表达式进行高级文件名匹配</p>
        <p>• 组合多个条件可以精确筛选文件</p>
        <p>• 大文件搜索时建议设置大小范围</p>
        <p>• 定期导出重要的配置文件作为备份</p>
        """

        msg = QMessageBox(self)
        msg.setWindowTitle("使用指南")
        msg.setText(help_text)
        msg.setTextFormat(Qt.TextFormat.RichText)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()

    def check_updates(self):
        """检查更新"""
        QMessageBox.information(
            self, "检查更新",
            "当前版本：1.0.0\n\n"
            "这是一个独立的桌面应用程序。\n"
            "如需获取最新版本，请联系开发者。\n\n"
            "开发者微信：yangjun_127"
        )

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h2>文件筛选过滤工具</h2>
        <p><b>版本:</b> 1.0.0</p>
        <p><b>界面:</b> PySide6 (Qt6)</p>
        <p><b>作者:</b> Andy_127【浅醉丶墨语】</p>
        <br>
        <p>一个功能强大的文件筛选和管理工具，支持：</p>
        <ul>
        <li>🔍 多种筛选条件（扩展名、大小、时间、名称模式）</li>
        <li>🔄 重复文件检测（基于MD5哈希值精确匹配）</li>
        <li>📁 文件操作（复制、移动、删除）</li>
        <li>⚙️ 配置管理（保存和加载筛选规则）</li>
        <li>🎨 现代化的图形界面</li>
        <li>📤 导入导出功能</li>
        <li>⌨️ 快捷键支持</li>
        <li>🔇 无窗口启动（隐藏命令行）</li>
        </ul>
        <br>
        <p><b>技术特性:</b></p>
        <ul>
        <li>基于 Python + PySide6 开发</li>
        <li>多线程文件操作，界面不卡顿</li>
        <li>支持正则表达式匹配</li>
        <li>完善的错误处理机制</li>
        </ul>
        <br>
        <p><b>联系方式:</b></p>
        <p>微信: yangjun_127</p>
        <p>如有问题或建议，欢迎联系！</p>
        """

        QMessageBox.about(self, "关于", about_text)

    def closeEvent(self, event):
        """关闭事件"""
        # 停止正在运行的线程
        if self.search_thread and self.search_thread.isRunning():
            self.search_thread.terminate()
            self.search_thread.wait()

        if self.operation_thread and self.operation_thread.isRunning():
            self.operation_thread.terminate()
            self.operation_thread.wait()

        event.accept()

def main():
    """主函数"""
    # 检查PySide6是否可用
    if not PYSIDE6_AVAILABLE:
        print("错误: PySide6不可用，无法启动图形界面")
        print("请运行: pip install PySide6")
        return False

    try:
        app = QApplication(sys.argv)

        # 设置应用程序信息
        app.setApplicationName("文件筛选过滤工具")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("FileFilter")

        # 设置应用程序图标
        try:
            project_root = Path(__file__).parent.parent.parent
            icon_path = project_root / "assets" / "res" / "logo.ico"
            if icon_path.exists():
                app.setWindowIcon(QIcon(str(icon_path)))
            else:
                icon_path = project_root / "assets" / "res" / "logo.png"
                if icon_path.exists():
                    app.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            print(f"设置应用程序图标时出错: {e}")

        # 设置应用程序样式
        app.setStyle("Fusion")

        # 创建主窗口
        window = FileFilterMainWindow()
        window.show()

        # 运行应用程序
        sys.exit(app.exec())
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    main()
