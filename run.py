#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 主启动脚本
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("🚀 文件筛选过滤工具 v1.2")
    print("=" * 40)
    print("✨ 特性: Windows渐变主题 + 复选框✓符号")
    print("🎨 主题: 支持Windows/VS主题切换 (Ctrl+T)")
    print("📐 布局: 响应式设计，适配各种屏幕")
    print()

    try:
        # 启动重构后的PySide6版本
        from file_filter_tool.gui_main import main as gui_main
        gui_main()
    except ImportError as e:
        print("❌ 错误：无法导入PySide6模块")
        print("📦 请确保已安装PySide6：pip install PySide6")
        print(f"🔍 详细错误：{e}")
        print()
        print("💡 安装命令:")
        print("   pip install PySide6")
        print("   或")
        print("   pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 启动失败：{e}")
        return False

    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
