#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 主启动脚本
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    try:
        # 启动PySide6版本
        from file_filter_tool.gui_pyside6 import main as pyside6_main
        pyside6_main()
    except ImportError as e:
        print("错误：无法导入PySide6模块")
        print("请确保已安装PySide6：pip install PySide6")
        print(f"详细错误：{e}")
        return False
    except Exception as e:
        print(f"启动失败：{e}")
        return False

    return True

if __name__ == '__main__':
    main()
