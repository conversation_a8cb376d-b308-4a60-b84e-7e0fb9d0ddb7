"""
编号文件处理模块
处理同编号文件的检测、分组和时间筛选
"""

import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict

class NumberedFileDetector:
    """编号文件检测器"""
    
    # 编号模式正则表达式
    PATTERNS = {
        'suffix_number': r'^(.+?)_?(\d+)(\.[^.]*)?$',           # file_001.txt
        'prefix_number': r'^(\d+)_?(.+)$',                      # 001_file.txt
        'version_number': r'^(.+?)v(\d+(?:\.\d+)*)(.*)$',       # file_v1.2.txt
        'date_number': r'^(.+?)(\d{4}-?\d{2}-?\d{2})(.*)$',     # file_20240101.txt
        'timestamp_number': r'^(.+?)(\d{10,13})(.*)$',          # file_1704067200.txt
        'middle_number': r'^(.+?)_(\d+)_(.+)$',                 # file_001_final.txt
    }
    
    def __init__(self):
        self.compiled_patterns = {
            name: re.compile(pattern, re.IGNORECASE)
            for name, pattern in self.PATTERNS.items()
        }
    
    def detect_pattern(self, filename: str) -> Optional[Dict[str, Any]]:
        """检测文件名中的编号模式"""
        name_only = Path(filename).stem  # 去除扩展名
        
        for pattern_name, compiled_pattern in self.compiled_patterns.items():
            match = compiled_pattern.match(name_only)
            if match:
                groups = match.groups()
                
                if pattern_name == 'suffix_number':
                    return {
                        'type': pattern_name,
                        'base_name': groups[0],
                        'number': groups[1],
                        'extension': Path(filename).suffix
                    }
                elif pattern_name == 'prefix_number':
                    return {
                        'type': pattern_name,
                        'base_name': groups[1],
                        'number': groups[0],
                        'extension': Path(filename).suffix
                    }
                elif pattern_name == 'version_number':
                    return {
                        'type': pattern_name,
                        'base_name': groups[0],
                        'number': groups[1],
                        'suffix': groups[2],
                        'extension': Path(filename).suffix
                    }
                elif pattern_name == 'middle_number':
                    return {
                        'type': pattern_name,
                        'base_name': f"{groups[0]}_{groups[2]}",
                        'number': groups[1],
                        'extension': Path(filename).suffix
                    }
                else:
                    return {
                        'type': pattern_name,
                        'base_name': groups[0],
                        'number': groups[1],
                        'suffix': groups[2] if len(groups) > 2 else '',
                        'extension': Path(filename).suffix
                    }
        
        return None
    
    def extract_base_name(self, filename: str, ignore_extension: bool = True) -> str:
        """提取基础文件名（去除编号部分）"""
        pattern_info = self.detect_pattern(filename)
        if pattern_info:
            base = pattern_info['base_name']
            if not ignore_extension:
                base += pattern_info['extension']
            return base
        
        # 如果没有检测到模式，返回原文件名
        return Path(filename).stem if ignore_extension else filename

class NumberedFileGrouper:
    """编号文件分组器"""
    
    def __init__(self, detector: NumberedFileDetector):
        self.detector = detector
    
    def group_files(self, files: List[Path], 
                   ignore_extension: bool = True,
                   case_sensitive: bool = False) -> Dict[str, List[Dict[str, Any]]]:
        """将文件按编号模式分组"""
        groups = defaultdict(list)
        
        for file_path in files:
            try:
                # 获取文件信息
                stat = file_path.stat()
                filename = file_path.name
                
                # 检测编号模式
                pattern_info = self.detector.detect_pattern(filename)
                
                if pattern_info:
                    # 生成分组键
                    base_name = pattern_info['base_name']
                    if not case_sensitive:
                        base_name = base_name.lower()
                    
                    if not ignore_extension:
                        base_name += pattern_info['extension']
                    
                    # 添加到分组
                    file_info = {
                        'file': file_path,
                        'filename': filename,
                        'pattern_info': pattern_info,
                        'number': pattern_info['number'],
                        'mtime': stat.st_mtime,
                        'ctime': stat.st_ctime,
                        'size': stat.st_size,
                        'mtime_str': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        'ctime_str': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                    }
                    
                    groups[base_name].append(file_info)
            
            except (OSError, PermissionError):
                # 跳过无法访问的文件
                continue
        
        # 只返回有多个文件的分组（编号文件组）
        return {k: v for k, v in groups.items() if len(v) > 1}

class TimeFilterStrategy:
    """时间筛选策略"""
    
    STRATEGIES = {
        'keep_newest': '保留最新',
        'keep_oldest': '保留最旧',
        'keep_newest_n': '保留最新N个',
        'keep_oldest_n': '保留最旧N个',
        'keep_time_range': '保留时间范围',
        'keep_by_interval': '按时间间隔保留'
    }
    
    @staticmethod
    def apply_strategy(file_group: List[Dict[str, Any]], 
                      strategy: str, 
                      options: Dict[str, Any] = None) -> List[Path]:
        """对文件组应用时间筛选策略"""
        if not file_group:
            return []
        
        options = options or {}
        
        if strategy == 'keep_newest':
            # 保留最新的文件
            newest = max(file_group, key=lambda x: x['mtime'])
            return [newest['file']]
        
        elif strategy == 'keep_oldest':
            # 保留最旧的文件
            oldest = min(file_group, key=lambda x: x['mtime'])
            return [oldest['file']]
        
        elif strategy == 'keep_newest_n':
            # 保留最新的N个文件
            count = options.get('count', 1)
            sorted_files = sorted(file_group, key=lambda x: x['mtime'], reverse=True)
            return [f['file'] for f in sorted_files[:count]]
        
        elif strategy == 'keep_oldest_n':
            # 保留最旧的N个文件
            count = options.get('count', 1)
            sorted_files = sorted(file_group, key=lambda x: x['mtime'])
            return [f['file'] for f in sorted_files[:count]]
        
        elif strategy == 'keep_time_range':
            # 保留指定时间范围内的文件
            start_time = options.get('start_time')
            end_time = options.get('end_time')
            if start_time and end_time:
                filtered = []
                for file_info in file_group:
                    if start_time <= file_info['mtime'] <= end_time:
                        filtered.append(file_info['file'])
                return filtered
            return []
        
        elif strategy == 'keep_by_interval':
            # 按时间间隔保留文件
            interval_days = options.get('interval_days', 7)
            interval_seconds = interval_days * 24 * 3600
            
            sorted_files = sorted(file_group, key=lambda x: x['mtime'])
            if not sorted_files:
                return []
            
            result = [sorted_files[0]['file']]  # 保留第一个
            last_kept_time = sorted_files[0]['mtime']
            
            for file_info in sorted_files[1:]:
                if file_info['mtime'] - last_kept_time >= interval_seconds:
                    result.append(file_info['file'])
                    last_kept_time = file_info['mtime']
            
            return result
        
        # 默认返回所有文件
        return [f['file'] for f in file_group]

class NumberedFileFilter:
    """编号文件筛选器主类"""
    
    def __init__(self):
        self.detector = NumberedFileDetector()
        self.grouper = NumberedFileGrouper(self.detector)
        self.strategy = TimeFilterStrategy()
    
    def filter_numbered_files(self, files: List[Path], 
                            time_strategy: str = 'keep_newest',
                            strategy_options: Dict[str, Any] = None,
                            ignore_extension: bool = True,
                            case_sensitive: bool = False) -> Tuple[List[Path], Dict[str, Any]]:
        """筛选编号文件"""
        
        # 分组文件
        groups = self.grouper.group_files(
            files, 
            ignore_extension=ignore_extension,
            case_sensitive=case_sensitive
        )
        
        # 应用时间筛选策略
        filtered_files = []
        group_results = {}
        
        for group_name, file_list in groups.items():
            kept_files = self.strategy.apply_strategy(
                file_list, 
                time_strategy, 
                strategy_options
            )
            
            filtered_files.extend(kept_files)
            
            # 记录分组结果
            group_results[group_name] = {
                'total_files': len(file_list),
                'kept_files': len(kept_files),
                'filtered_files': len(file_list) - len(kept_files),
                'files': file_list,
                'kept': kept_files
            }
        
        # 添加非编号文件（没有被分组的文件）
        all_grouped_files = set()
        for file_list in groups.values():
            for file_info in file_list:
                all_grouped_files.add(file_info['file'])
        
        non_numbered_files = [f for f in files if f not in all_grouped_files]
        filtered_files.extend(non_numbered_files)
        
        # 统计信息
        stats = {
            'total_input_files': len(files),
            'numbered_groups': len(groups),
            'total_numbered_files': sum(len(file_list) for file_list in groups.values()),
            'non_numbered_files': len(non_numbered_files),
            'total_kept_files': len(filtered_files),
            'total_filtered_files': len(files) - len(filtered_files),
            'groups': group_results
        }
        
        return filtered_files, stats
