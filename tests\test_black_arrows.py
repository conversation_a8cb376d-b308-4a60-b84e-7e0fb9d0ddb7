#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黑色箭头测试
测试恢复到系统默认的黑色箭头效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QSpinBox, QComboBox, QDateEdit
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.themes import WindowsTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class BlackArrowTestWidget(QWidget):
    """黑色箭头测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("黑色箭头测试 - 系统默认 + 上下布局")
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
⚫ 黑色箭头测试

本测试验证恢复到系统默认的黑色箭头效果：

🎯 设计特点：
• 系统默认样式：使用Qt原生的按钮样式
• 黑色箭头：清晰的黑色三角形箭头
• 上下布局：SpinBox的▲▼按钮垂直排列
• 简洁设计：无复杂渐变，保持简洁美观
• 标准尺寸：4px×6px的标准箭头大小

✅ 优势：
• 兼容性最佳：系统原生支持
• 清晰度最高：黑色箭头对比度强
• 性能最优：无复杂CSS计算
• 维护简单：代码简洁易懂
• 用户熟悉：符合系统习惯

🔍 测试重点：
• SpinBox的▲▼箭头是否为上下布局
• 箭头颜色是否为清晰的黑色
• ComboBox和DateEdit的▼箭头是否清晰
• 按钮功能是否正常工作
• 整体视觉效果是否协调
        """)
        layout.addWidget(info_label)
        
        # 测试组1：SpinBox上下布局测试
        spinbox_group = QGroupBox("SpinBox上下布局测试（黑色箭头）")
        spinbox_layout = QVBoxLayout(spinbox_group)
        
        spinbox_tests = [
            ("数量选择", 1, 100, 5, " 个"),
            ("间隔天数", 1, 365, 7, " 天"),
            ("百分比", 0, 100, 50, " %"),
            ("文件数量", 1, 999, 10, " 个"),
            ("时间间隔", 1, 30, 3, " 天"),
        ]
        
        for label_text, min_val, max_val, default_val, suffix in spinbox_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            if suffix:
                spinbox.setSuffix(suffix)
            spinbox.setMinimumWidth(90)
            spinbox.setMaximumWidth(110)
            row_layout.addWidget(spinbox)
            
            # 添加说明
            desc_label = QLabel("← 上下布局▲▼，黑色箭头")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            spinbox_layout.addLayout(row_layout)
        
        layout.addWidget(spinbox_group)
        
        # 测试组2：ComboBox黑色箭头测试
        combo_group = QGroupBox("ComboBox黑色箭头测试")
        combo_layout = QVBoxLayout(combo_group)
        
        combo_tests = [
            ("策略选择", ["保留最新", "保留最旧", "保留最新N个", "保留最旧N个"]),
            ("文件类型", [".txt", ".py", ".jpg", ".pdf", ".doc"]),
            ("排序方式", ["按名称", "按大小", "按时间", "按类型"]),
            ("主题选择", ["Windows主题", "默认主题", "系统主题"]),
        ]
        
        for label_text, items in combo_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            combo = QComboBox()
            combo.addItems(items)
            combo.setMinimumWidth(120)
            combo.setMaximumWidth(150)
            row_layout.addWidget(combo)
            
            # 添加说明
            desc_label = QLabel("← 黑色下拉▼箭头")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            combo_layout.addLayout(row_layout)
        
        layout.addWidget(combo_group)
        
        # 测试组3：DateEdit黑色箭头测试（加宽版本）
        date_group = QGroupBox("DateEdit黑色箭头测试（加宽130-160px）")
        date_layout = QVBoxLayout(date_group)
        
        date_tests = [
            ("开始日期", -30),
            ("结束日期", 0),
            ("创建日期", -7),
            ("修改日期", -1),
        ]
        
        for label_text, days_offset in date_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            date_edit = QDateEdit()
            date_edit.setCalendarPopup(True)
            date_edit.setDate(QDate.currentDate().addDays(days_offset))
            date_edit.setDisplayFormat("yyyy年MM月dd日")
            date_edit.setMinimumWidth(130)  # 加宽后的尺寸
            date_edit.setMaximumWidth(160)
            row_layout.addWidget(date_edit)
            
            # 添加说明
            desc_label = QLabel("← 加宽日期选择器，黑色▼箭头")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            date_layout.addLayout(row_layout)
        
        layout.addWidget(date_group)
        
        # 测试说明
        test_info = QLabel("""
⚫ 黑色箭头设计说明：

✅ SpinBox箭头：
• 布局：上下垂直排列（▲在上，▼在下）
• 颜色：纯黑色，对比度强
• 尺寸：4px×6px标准大小
• 样式：系统默认，兼容性最佳

✅ ComboBox/DateEdit箭头：
• 颜色：纯黑色下拉箭头
• 尺寸：4px×6px标准大小
• 功能：点击弹出下拉列表/日历
• 样式：简洁清晰

✅ 日期选择器加宽：
• 宽度：130-160px（原100-120px）
• 格式：yyyy年MM月dd日完整显示
• 功能：日历弹出选择

🎯 测试方法：
1. 观察SpinBox是否为上下布局
2. 检查所有箭头是否为清晰的黑色
3. 测试按钮点击功能是否正常
4. 验证日期选择器宽度是否合适
5. 确认整体视觉效果协调
        """)
        test_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(test_info)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(WindowsTheme.get_complete_style())

def test_black_arrows():
    """测试黑色箭头"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行黑色箭头测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("⚫ 黑色箭头测试启动")
    print("=" * 50)
    print("🎯 设计特点:")
    print("  • 系统默认样式：Qt原生按钮样式")
    print("  • 黑色箭头：清晰的黑色三角形")
    print("  • 上下布局：SpinBox垂直排列")
    print("  • 简洁设计：无复杂渐变效果")
    print("  • 标准尺寸：4px×6px箭头大小")
    print()
    print("✅ 优势:")
    print("  • 兼容性最佳：系统原生支持")
    print("  • 清晰度最高：黑色对比度强")
    print("  • 性能最优：无复杂CSS计算")
    print("  • 维护简单：代码简洁易懂")
    print("  • 用户熟悉：符合系统习惯")
    print()
    print("🔍 测试重点:")
    print("  • SpinBox上下布局的▲▼箭头")
    print("  • 所有箭头的黑色显示效果")
    print("  • 日期选择器的加宽效果（130-160px）")
    print("  • 按钮功能的正常工作")
    print("  • 整体视觉效果的协调性")
    print()
    
    # 创建测试窗口
    test_widget = BlackArrowTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行黑色箭头测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_black_arrows()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
