#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
箭头符号测试
测试更新后的箭头符号显示效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QSpinBox, QComboBox, QDateEdit
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.themes import WindowsTheme, VSTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class ArrowSymbolTestWidget(QWidget):
    """箭头符号测试控件"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "windows"
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("箭头符号测试 - 更新后的▲▼符号")
        self.setMinimumSize(700, 600)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🔺 箭头符号更新测试

本测试验证更新后的箭头符号显示效果：

🎯 更新内容：
• 箭头大小优化：从5px调整为4px宽，7px高
• 箭头形状更清晰：更标准的三角形
• 悬停效果：白色箭头
• 按下效果：浅灰色箭头

💡 测试重点：
• SpinBox的▲▼箭头是否清晰可见
• ComboBox的▼箭头是否清晰可见
• DateEdit的▼箭头是否清晰可见
• 悬停时箭头颜色变化
• 按下时箭头颜色变化
• 不同主题下的显示效果

🔍 观察要点：
• 箭头形状是否标准
• 箭头大小是否合适
• 颜色对比是否清晰
• 交互反馈是否明显
        """)
        layout.addWidget(info_label)
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.theme_btn = QPushButton("切换到 VS 主题")
        self.theme_btn.clicked.connect(self.switch_theme)
        theme_layout.addWidget(self.theme_btn)
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 测试组1：SpinBox箭头测试
        spinbox_group = QGroupBox("SpinBox箭头测试 (▲▼)")
        spinbox_layout = QVBoxLayout(spinbox_group)
        
        spinbox_tests = [
            ("基本数字", 0, 100, 50, ""),
            ("带后缀", 1, 999, 10, " 个"),
            ("百分比", 0, 100, 75, " %"),
            ("天数", 1, 365, 30, " 天"),
            ("大数字", 0, 9999, 1000, ""),
        ]
        
        for label_text, min_val, max_val, default_val, suffix in spinbox_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            if suffix:
                spinbox.setSuffix(suffix)
            spinbox.setMinimumWidth(100)
            spinbox.setMaximumWidth(120)
            row_layout.addWidget(spinbox)
            
            # 添加说明
            desc_label = QLabel("← 测试▲▼箭头")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            spinbox_layout.addLayout(row_layout)
        
        layout.addWidget(spinbox_group)
        
        # 测试组2：ComboBox箭头测试
        combo_group = QGroupBox("ComboBox箭头测试 (▼)")
        combo_layout = QVBoxLayout(combo_group)
        
        combo_tests = [
            ("策略选择", ["保留最新", "保留最旧", "保留最新N个", "保留最旧N个"]),
            ("文件类型", [".txt", ".py", ".jpg", ".pdf", ".doc"]),
            ("排序方式", ["按名称", "按大小", "按时间", "按类型"]),
            ("主题选择", ["Windows主题", "VS主题", "自定义主题"]),
        ]
        
        for label_text, items in combo_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            combo = QComboBox()
            combo.addItems(items)
            combo.setMinimumWidth(120)
            combo.setMaximumWidth(150)
            row_layout.addWidget(combo)
            
            # 添加说明
            desc_label = QLabel("← 测试▼箭头")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            combo_layout.addLayout(row_layout)
        
        layout.addWidget(combo_group)
        
        # 测试组3：DateEdit箭头测试
        date_group = QGroupBox("DateEdit箭头测试 (▼)")
        date_layout = QVBoxLayout(date_group)
        
        date_tests = [
            ("开始日期", -30),
            ("结束日期", 0),
            ("创建日期", -7),
            ("修改日期", -1),
        ]
        
        for label_text, days_offset in date_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            date_edit = QDateEdit()
            date_edit.setCalendarPopup(True)
            date_edit.setDate(QDate.currentDate().addDays(days_offset))
            date_edit.setDisplayFormat("yyyy年MM月dd日")
            date_edit.setMinimumWidth(120)
            date_edit.setMaximumWidth(150)
            row_layout.addWidget(date_edit)
            
            # 添加说明
            desc_label = QLabel("← 测试▼箭头和日历弹出")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            date_layout.addLayout(row_layout)
        
        layout.addWidget(date_group)
        
        # 测试说明
        test_info = QLabel("""
🔍 测试方法：
1. 悬停在各种箭头按钮上，观察颜色变化
2. 点击箭头按钮，观察按下效果
3. 切换主题，观察不同主题下的箭头显示
4. 特别注意箭头的清晰度和大小

✅ 预期效果：
• 默认状态：深色箭头，清晰可见
• 悬停状态：白色箭头，对比明显
• 按下状态：浅灰色箭头，反馈清晰
• 箭头形状：标准三角形，大小适中
        """)
        test_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(test_info)
    
    def apply_theme(self):
        """应用主题"""
        if self.current_theme == "windows":
            self.setStyleSheet(WindowsTheme.get_complete_style())
        else:
            self.setStyleSheet(VSTheme.get_complete_style())
    
    def switch_theme(self):
        """切换主题"""
        if self.current_theme == "windows":
            self.current_theme = "vs"
            self.theme_btn.setText("切换到 Windows 主题")
            print("🌙 切换到 Visual Studio 深色主题")
        else:
            self.current_theme = "windows"
            self.theme_btn.setText("切换到 VS 主题")
            print("☀️ 切换到 Windows 浅色主题")
        
        self.apply_theme()

def test_arrow_symbols():
    """测试箭头符号"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行箭头符号测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🔺 箭头符号更新测试启动")
    print("=" * 50)
    print("📋 更新内容:")
    print("  • 箭头尺寸：4px宽 × 7px高")
    print("  • 箭头形状：更标准的三角形")
    print("  • 默认颜色：深色（主题文字颜色）")
    print("  • 悬停颜色：白色")
    print("  • 按下颜色：浅灰色 (#e0e0e0)")
    print()
    print("💡 测试重点:")
    print("  • SpinBox的▲▼箭头清晰度")
    print("  • ComboBox的▼箭头清晰度")
    print("  • DateEdit的▼箭头清晰度")
    print("  • 悬停和按下时的颜色变化")
    print("  • 不同主题下的显示效果")
    print()
    print("🎯 验证方法:")
    print("  • 悬停在箭头按钮上观察颜色变化")
    print("  • 点击箭头按钮测试交互效果")
    print("  • 切换主题观察箭头在不同主题下的显示")
    print("  • 检查箭头大小和形状是否合适")
    print()
    
    # 创建测试窗口
    test_widget = ArrowSymbolTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行箭头符号测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_arrow_symbols()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
