#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量操作标签页
提供文件的批量操作和分析功能
"""

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
        QGroupBox, QPushButton, QTableWidget, QTableWidgetItem,
        QHeaderView, QProgressBar, QTextEdit, QLabel,
        QComboBox, QCheckBox, QSpinBox, QLineEdit,
        QFrame, QListWidget, QListWidgetItem, QTabWidget
    )
    from PySide6.QtCore import Qt, Signal
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

from .components import SearchPathWidget


class BatchOperationsTab(QWidget):
    """批量操作标签页"""
    
    # 信号定义
    status_message = Signal(str)
    progress_update = Signal(int, int, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.file_list = []
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建子标签页
        self.sub_tab_widget = QTabWidget()
        
        # 批量重命名标签页
        rename_tab = self.create_batch_rename_tab()
        self.sub_tab_widget.addTab(rename_tab, "📝 批量重命名")
        
        # 文件分析标签页
        analysis_tab = self.create_file_analysis_tab()
        self.sub_tab_widget.addTab(analysis_tab, "📊 文件分析")
        
        # 批量转换标签页
        convert_tab = self.create_batch_convert_tab()
        self.sub_tab_widget.addTab(convert_tab, "🔄 批量转换")
        
        # 文件整理标签页
        organize_tab = self.create_file_organize_tab()
        self.sub_tab_widget.addTab(organize_tab, "📁 文件整理")
        
        layout.addWidget(self.sub_tab_widget)
        
        # 底部：操作日志
        bottom_widget = self.create_bottom_widget()
        layout.addWidget(bottom_widget)
    
    def create_batch_rename_tab(self):
        """创建批量重命名标签页"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 左侧：设置面板
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)
        
        # 文件选择
        file_group = QGroupBox("📁 文件选择")
        file_layout = QVBoxLayout(file_group)
        
        self.rename_path_widget = SearchPathWidget()
        file_layout.addWidget(self.rename_path_widget)
        
        # 文件筛选
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("文件类型:"))
        self.rename_file_type_combo = QComboBox()
        self.rename_file_type_combo.addItems([
            "所有文件", "图片文件", "视频文件", "音频文件", "文档文件"
        ])
        filter_layout.addWidget(self.rename_file_type_combo)
        filter_layout.addStretch()
        file_layout.addLayout(filter_layout)
        
        left_layout.addWidget(file_group)
        
        # 重命名规则
        rule_group = QGroupBox("📝 重命名规则")
        rule_layout = QVBoxLayout(rule_group)
        
        # 重命名模式
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("重命名模式:"))
        self.rename_mode_combo = QComboBox()
        self.rename_mode_combo.addItems([
            "前缀 + 原名",
            "原名 + 后缀",
            "替换文本",
            "序号重命名",
            "日期重命名",
            "自定义规则"
        ])
        mode_layout.addWidget(self.rename_mode_combo)
        rule_layout.addLayout(mode_layout)
        
        # 规则设置
        self.rename_prefix_edit = QLineEdit()
        self.rename_prefix_edit.setPlaceholderText("输入前缀...")
        rule_layout.addWidget(self.rename_prefix_edit)
        
        self.rename_suffix_edit = QLineEdit()
        self.rename_suffix_edit.setPlaceholderText("输入后缀...")
        rule_layout.addWidget(self.rename_suffix_edit)
        
        # 序号设置
        number_layout = QHBoxLayout()
        number_layout.addWidget(QLabel("起始序号:"))
        self.start_number_spin = QSpinBox()
        self.start_number_spin.setRange(0, 99999)
        self.start_number_spin.setValue(1)
        number_layout.addWidget(self.start_number_spin)
        
        number_layout.addWidget(QLabel("位数:"))
        self.number_digits_spin = QSpinBox()
        self.number_digits_spin.setRange(1, 10)
        self.number_digits_spin.setValue(3)
        number_layout.addWidget(self.number_digits_spin)
        number_layout.addStretch()
        rule_layout.addLayout(number_layout)
        
        left_layout.addWidget(rule_group)
        
        # 操作按钮
        btn_layout = QVBoxLayout()
        
        self.scan_rename_btn = QPushButton("🔍 扫描文件")
        btn_layout.addWidget(self.scan_rename_btn)
        
        self.preview_rename_btn = QPushButton("👁️ 预览重命名")
        self.preview_rename_btn.setEnabled(False)
        btn_layout.addWidget(self.preview_rename_btn)
        
        self.execute_rename_btn = QPushButton("✅ 执行重命名")
        self.execute_rename_btn.setEnabled(False)
        btn_layout.addWidget(self.execute_rename_btn)
        
        left_layout.addLayout(btn_layout)
        left_layout.addStretch()
        
        layout.addWidget(left_widget)
        
        # 右侧：预览列表
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        preview_label = QLabel("📋 重命名预览")
        preview_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        right_layout.addWidget(preview_label)
        
        self.rename_preview_table = QTableWidget()
        self.rename_preview_table.setColumnCount(3)
        self.rename_preview_table.setHorizontalHeaderLabels(["原文件名", "新文件名", "状态"])
        
        header = self.rename_preview_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        right_layout.addWidget(self.rename_preview_table)
        
        layout.addWidget(right_widget)
        
        # 设置分割比例
        layout.setStretch(0, 1)
        layout.setStretch(1, 2)
        
        return widget
    
    def create_file_analysis_tab(self):
        """创建文件分析标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 分析设置
        settings_frame = QFrame()
        settings_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        settings_frame.setMaximumHeight(80)
        
        settings_layout = QHBoxLayout(settings_frame)
        settings_layout.setContentsMargins(15, 10, 15, 10)
        
        # 分析路径
        self.analysis_path_widget = SearchPathWidget()
        settings_layout.addWidget(self.analysis_path_widget)
        
        # 分析类型
        settings_layout.addWidget(QLabel("分析类型:"))
        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "文件大小分布",
            "文件类型统计",
            "修改时间分析",
            "目录结构分析",
            "重复文件统计"
        ])
        settings_layout.addWidget(self.analysis_type_combo)
        
        # 分析按钮
        self.start_analysis_btn = QPushButton("📊 开始分析")
        self.start_analysis_btn.setMinimumWidth(100)
        settings_layout.addWidget(self.start_analysis_btn)
        
        layout.addWidget(settings_frame)
        
        # 分析结果显示
        result_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：统计图表区域
        chart_group = QGroupBox("📈 统计图表")
        chart_layout = QVBoxLayout(chart_group)
        
        self.chart_placeholder = QLabel("📊 图表将在这里显示")
        self.chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.chart_placeholder.setStyleSheet(
            "border: 2px dashed #bdc3c7; color: #7f8c8d; font-size: 14px; min-height: 200px;"
        )
        chart_layout.addWidget(self.chart_placeholder)
        
        result_splitter.addWidget(chart_group)
        
        # 右侧：详细数据
        data_group = QGroupBox("📋 详细数据")
        data_layout = QVBoxLayout(data_group)
        
        self.analysis_table = QTableWidget()
        self.analysis_table.setColumnCount(3)
        self.analysis_table.setHorizontalHeaderLabels(["项目", "数量", "占比"])
        
        header = self.analysis_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        data_layout.addWidget(self.analysis_table)
        
        # 导出按钮
        export_layout = QHBoxLayout()
        export_layout.addStretch()
        
        self.export_chart_btn = QPushButton("📊 导出图表")
        self.export_chart_btn.setEnabled(False)
        export_layout.addWidget(self.export_chart_btn)
        
        self.export_data_btn = QPushButton("📋 导出数据")
        self.export_data_btn.setEnabled(False)
        export_layout.addWidget(self.export_data_btn)
        
        data_layout.addLayout(export_layout)
        
        result_splitter.addWidget(data_group)
        result_splitter.setSizes([400, 400])
        
        layout.addWidget(result_splitter)
        
        return widget
    
    def create_batch_convert_tab(self):
        """创建批量转换标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 转换设置
        settings_group = QGroupBox("🔄 转换设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 源文件设置
        source_layout = QHBoxLayout()
        source_layout.addWidget(QLabel("源文件类型:"))
        self.source_format_combo = QComboBox()
        self.source_format_combo.addItems([
            "图片文件 (jpg, png, bmp, gif)",
            "文档文件 (doc, docx, pdf, txt)",
            "音频文件 (mp3, wav, flac, aac)",
            "视频文件 (mp4, avi, mkv, mov)"
        ])
        source_layout.addWidget(self.source_format_combo)
        source_layout.addStretch()
        settings_layout.addLayout(source_layout)
        
        # 目标格式设置
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("目标格式:"))
        self.target_format_combo = QComboBox()
        self.target_format_combo.addItems(["PNG", "JPG", "PDF", "TXT"])
        target_layout.addWidget(self.target_format_combo)
        
        target_layout.addWidget(QLabel("质量:"))
        self.quality_spin = QSpinBox()
        self.quality_spin.setRange(1, 100)
        self.quality_spin.setValue(90)
        self.quality_spin.setSuffix("%")
        target_layout.addWidget(self.quality_spin)
        target_layout.addStretch()
        settings_layout.addLayout(target_layout)
        
        layout.addWidget(settings_group)
        
        # 文件列表和操作
        main_layout = QHBoxLayout()
        
        # 左侧：文件列表
        file_list_group = QGroupBox("📁 待转换文件")
        file_list_layout = QVBoxLayout(file_list_group)
        
        self.convert_file_list = QListWidget()
        file_list_layout.addWidget(self.convert_file_list)
        
        # 文件操作按钮
        file_btn_layout = QHBoxLayout()
        
        self.add_files_btn = QPushButton("➕ 添加文件")
        file_btn_layout.addWidget(self.add_files_btn)
        
        self.remove_files_btn = QPushButton("➖ 移除文件")
        file_btn_layout.addWidget(self.remove_files_btn)
        
        self.clear_files_btn = QPushButton("🗑️ 清空列表")
        file_btn_layout.addWidget(self.clear_files_btn)
        
        file_list_layout.addLayout(file_btn_layout)
        main_layout.addWidget(file_list_group)
        
        # 右侧：转换控制
        control_group = QGroupBox("⚙️ 转换控制")
        control_layout = QVBoxLayout(control_group)
        
        # 输出设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("选择输出目录...")
        output_layout.addWidget(self.output_dir_edit)
        
        self.browse_output_btn = QPushButton("📁")
        self.browse_output_btn.setMaximumWidth(40)
        output_layout.addWidget(self.browse_output_btn)
        control_layout.addLayout(output_layout)
        
        # 转换选项
        self.overwrite_check = QCheckBox("覆盖已存在文件")
        control_layout.addWidget(self.overwrite_check)
        
        self.keep_structure_check = QCheckBox("保持目录结构")
        self.keep_structure_check.setChecked(True)
        control_layout.addWidget(self.keep_structure_check)
        
        # 转换按钮
        convert_btn_layout = QVBoxLayout()
        
        self.start_convert_btn = QPushButton("🔄 开始转换")
        self.start_convert_btn.setMinimumHeight(40)
        self.start_convert_btn.setEnabled(False)
        convert_btn_layout.addWidget(self.start_convert_btn)
        
        self.pause_convert_btn = QPushButton("⏸️ 暂停转换")
        self.pause_convert_btn.setEnabled(False)
        convert_btn_layout.addWidget(self.pause_convert_btn)
        
        self.stop_convert_btn = QPushButton("⏹️ 停止转换")
        self.stop_convert_btn.setEnabled(False)
        convert_btn_layout.addWidget(self.stop_convert_btn)
        
        control_layout.addLayout(convert_btn_layout)
        control_layout.addStretch()
        
        main_layout.addWidget(control_group)
        
        layout.addLayout(main_layout)
        
        return widget
    
    def create_file_organize_tab(self):
        """创建文件整理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 整理规则设置
        rules_group = QGroupBox("📋 整理规则")
        rules_layout = QVBoxLayout(rules_group)
        
        # 整理方式
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("整理方式:"))
        self.organize_method_combo = QComboBox()
        self.organize_method_combo.addItems([
            "按文件类型分类",
            "按修改日期分类",
            "按文件大小分类",
            "按文件名规则分类",
            "自定义规则"
        ])
        method_layout.addWidget(self.organize_method_combo)
        method_layout.addStretch()
        rules_layout.addLayout(method_layout)
        
        # 目标目录设置
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("目标目录:"))
        self.organize_target_edit = QLineEdit()
        self.organize_target_edit.setPlaceholderText("选择整理目标目录...")
        target_layout.addWidget(self.organize_target_edit)
        
        self.browse_organize_btn = QPushButton("📁")
        self.browse_organize_btn.setMaximumWidth(40)
        target_layout.addWidget(self.browse_organize_btn)
        rules_layout.addLayout(target_layout)
        
        layout.addWidget(rules_group)
        
        # 整理预览和执行
        preview_group = QGroupBox("👁️ 整理预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.organize_preview_table = QTableWidget()
        self.organize_preview_table.setColumnCount(3)
        self.organize_preview_table.setHorizontalHeaderLabels(["源路径", "目标路径", "操作"])
        
        header = self.organize_preview_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        preview_layout.addWidget(self.organize_preview_table)
        
        # 操作按钮
        organize_btn_layout = QHBoxLayout()
        
        self.scan_organize_btn = QPushButton("🔍 扫描文件")
        organize_btn_layout.addWidget(self.scan_organize_btn)
        
        self.preview_organize_btn = QPushButton("👁️ 预览整理")
        self.preview_organize_btn.setEnabled(False)
        organize_btn_layout.addWidget(self.preview_organize_btn)
        
        self.execute_organize_btn = QPushButton("📁 执行整理")
        self.execute_organize_btn.setEnabled(False)
        organize_btn_layout.addWidget(self.execute_organize_btn)
        
        organize_btn_layout.addStretch()
        
        preview_layout.addLayout(organize_btn_layout)
        
        layout.addWidget(preview_group)
        
        return widget
    
    def create_bottom_widget(self):
        """创建底部控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("📝 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return widget
    
    def connect_signals(self):
        """连接信号"""
        # 批量重命名信号
        self.scan_rename_btn.clicked.connect(self.scan_rename_files)
        self.preview_rename_btn.clicked.connect(self.preview_rename)
        self.execute_rename_btn.clicked.connect(self.execute_rename)
        
        # 文件分析信号
        self.start_analysis_btn.clicked.connect(self.start_file_analysis)
        self.export_chart_btn.clicked.connect(self.export_analysis_chart)
        self.export_data_btn.clicked.connect(self.export_analysis_data)
        
        # 批量转换信号
        self.add_files_btn.clicked.connect(self.add_convert_files)
        self.remove_files_btn.clicked.connect(self.remove_convert_files)
        self.clear_files_btn.clicked.connect(self.clear_convert_files)
        self.start_convert_btn.clicked.connect(self.start_batch_convert)
        
        # 文件整理信号
        self.scan_organize_btn.clicked.connect(self.scan_organize_files)
        self.preview_organize_btn.clicked.connect(self.preview_organize)
        self.execute_organize_btn.clicked.connect(self.execute_organize)
    
    def scan_rename_files(self):
        """扫描重命名文件"""
        self.log_message("🔍 扫描重命名文件...")
        self.preview_rename_btn.setEnabled(True)
    
    def preview_rename(self):
        """预览重命名"""
        self.log_message("👁️ 预览重命名操作...")
        self.execute_rename_btn.setEnabled(True)
    
    def execute_rename(self):
        """执行重命名"""
        self.log_message("✅ 执行批量重命名...")
    
    def start_file_analysis(self):
        """开始文件分析"""
        self.log_message("📊 开始文件分析...")
        self.export_chart_btn.setEnabled(True)
        self.export_data_btn.setEnabled(True)
    
    def export_analysis_chart(self):
        """导出分析图表"""
        self.log_message("📊 导出分析图表...")
    
    def export_analysis_data(self):
        """导出分析数据"""
        self.log_message("📋 导出分析数据...")
    
    def add_convert_files(self):
        """添加转换文件"""
        self.log_message("➕ 添加转换文件...")
        self.start_convert_btn.setEnabled(True)
    
    def remove_convert_files(self):
        """移除转换文件"""
        self.log_message("➖ 移除转换文件...")
    
    def clear_convert_files(self):
        """清空转换文件"""
        self.convert_file_list.clear()
        self.log_message("🗑️ 清空转换文件列表")
    
    def start_batch_convert(self):
        """开始批量转换"""
        self.log_message("🔄 开始批量转换...")
    
    def scan_organize_files(self):
        """扫描整理文件"""
        self.log_message("🔍 扫描整理文件...")
        self.preview_organize_btn.setEnabled(True)
    
    def preview_organize(self):
        """预览整理"""
        self.log_message("👁️ 预览文件整理...")
        self.execute_organize_btn.setEnabled(True)
    
    def execute_organize(self):
        """执行整理"""
        self.log_message("📁 执行文件整理...")
    
    def log_message(self, message):
        """记录日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        self.log_text.ensureCursorVisible()
    
    def get_settings(self):
        """获取当前设置"""
        return {
            "current_sub_tab": self.sub_tab_widget.currentIndex(),
            "rename_mode": self.rename_mode_combo.currentText() if hasattr(self, 'rename_mode_combo') else "",
            "analysis_type": self.analysis_type_combo.currentText() if hasattr(self, 'analysis_type_combo') else ""
        }
    
    def load_settings(self, settings):
        """加载设置"""
        if "current_sub_tab" in settings:
            self.sub_tab_widget.setCurrentIndex(settings["current_sub_tab"])
    
    def clear_settings(self):
        """清空设置"""
        self.file_list.clear()
        if hasattr(self, 'convert_file_list'):
            self.convert_file_list.clear()
        self.log_message("🔄 已清空所有设置")
