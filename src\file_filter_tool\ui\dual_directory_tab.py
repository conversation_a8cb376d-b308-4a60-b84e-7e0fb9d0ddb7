#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双目录对比标签页
实现分屏对比界面，专业的双目录文件对比功能
"""

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
        QGroupBox, QPushButton, QTableWidget, QTableWidgetItem,
        QHeaderView, QProgressBar, QTextEdit, QLabel,
        QComboBox, QCheckBox, QLineEdit, QFileDialog,
        QFrame, QTreeWidget, QTreeWidgetItem, QMessageBox
    )
    from PySide6.QtCore import Qt, Signal
    from PySide6.QtGui import QFont
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

from pathlib import Path


class DualDirectoryTab(QWidget):
    """双目录对比标签页"""
    
    # 信号定义
    status_message = Signal(str)
    progress_update = Signal(int, int, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.root_path = None
        self.dir1_files = []
        self.dir2_files = []
        self.comparison_results = []
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 主分屏区域
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：目录1
        dir1_widget = self.create_directory_widget("📁 目录1", "dir1")
        main_splitter.addWidget(dir1_widget)
        
        # 中间：目录2
        dir2_widget = self.create_directory_widget("📁 目录2", "dir2")
        main_splitter.addWidget(dir2_widget)
        
        # 右侧：对比结果
        result_widget = self.create_comparison_result_widget()
        main_splitter.addWidget(result_widget)
        
        # 设置分割器比例
        main_splitter.setSizes([300, 300, 400])
        layout.addWidget(main_splitter)
        
        # 底部：操作日志和进度
        bottom_widget = self.create_bottom_widget()
        layout.addWidget(bottom_widget)
    
    def create_control_panel(self):
        """创建控制面板"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.StyledPanel)
        frame.setMaximumHeight(120)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)
        
        # 第一行：根目录选择
        root_layout = QHBoxLayout()
        
        root_label = QLabel("🏠 根目录:")
        root_label.setMinimumWidth(60)
        root_layout.addWidget(root_label)
        
        self.root_path_edit = QLineEdit()
        self.root_path_edit.setPlaceholderText("选择包含两个子目录的根目录...")
        root_layout.addWidget(self.root_path_edit)
        
        self.browse_root_btn = QPushButton("📁 浏览")
        self.browse_root_btn.setMaximumWidth(80)
        root_layout.addWidget(self.browse_root_btn)
        
        layout.addLayout(root_layout)
        
        # 第二行：子目录选择和模式设置
        settings_layout = QHBoxLayout()
        
        # 子目录选择
        self.dir1_combo = QComboBox()
        self.dir1_combo.setMinimumWidth(120)
        self.dir1_combo.setPlaceholderText("选择目录1")
        settings_layout.addWidget(QLabel("目录1:"))
        settings_layout.addWidget(self.dir1_combo)
        
        self.dir2_combo = QComboBox()
        self.dir2_combo.setMinimumWidth(120)
        self.dir2_combo.setPlaceholderText("选择目录2")
        settings_layout.addWidget(QLabel("目录2:"))
        settings_layout.addWidget(self.dir2_combo)
        
        settings_layout.addWidget(QLabel("|"))  # 分隔符
        
        # 对比设置
        settings_layout.addWidget(QLabel("匹配模式:"))
        self.match_mode_combo = QComboBox()
        self.match_mode_combo.addItems(["智能匹配", "完全匹配", "忽略扩展名", "编号匹配"])
        self.match_mode_combo.setMinimumWidth(100)
        settings_layout.addWidget(self.match_mode_combo)
        
        settings_layout.addWidget(QLabel("时间策略:"))
        self.time_strategy_combo = QComboBox()
        self.time_strategy_combo.addItems(["保留最新", "保留最旧", "手动选择", "仅显示"])
        self.time_strategy_combo.setMinimumWidth(100)
        settings_layout.addWidget(self.time_strategy_combo)
        
        settings_layout.addStretch()
        
        layout.addLayout(settings_layout)
        
        # 第三行：选项和操作按钮
        options_layout = QHBoxLayout()
        
        # 对比选项
        self.recursive_check = QCheckBox("包含子目录")
        self.recursive_check.setChecked(True)
        options_layout.addWidget(self.recursive_check)
        
        self.ignore_case_check = QCheckBox("忽略大小写")
        self.ignore_case_check.setChecked(True)
        options_layout.addWidget(self.ignore_case_check)
        
        self.content_compare_check = QCheckBox("内容对比")
        options_layout.addWidget(self.content_compare_check)
        
        self.safe_mode_check = QCheckBox("安全模式")
        self.safe_mode_check.setChecked(True)
        options_layout.addWidget(self.safe_mode_check)
        
        options_layout.addStretch()
        
        # 操作按钮
        self.scan_btn = QPushButton("🔍 扫描目录")
        self.scan_btn.setMinimumWidth(100)
        options_layout.addWidget(self.scan_btn)
        
        self.compare_btn = QPushButton("🔄 开始对比")
        self.compare_btn.setMinimumWidth(100)
        self.compare_btn.setEnabled(False)
        options_layout.addWidget(self.compare_btn)
        
        self.execute_btn = QPushButton("✅ 执行操作")
        self.execute_btn.setMinimumWidth(100)
        self.execute_btn.setEnabled(False)
        options_layout.addWidget(self.execute_btn)
        
        layout.addLayout(options_layout)
        
        return frame
    
    def create_directory_widget(self, title, dir_type):
        """创建目录显示控件"""
        group = QGroupBox(title)
        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)
        
        # 目录信息
        info_layout = QHBoxLayout()
        
        path_label = QLabel("路径: 未选择")
        path_label.setStyleSheet("color: #666666; font-size: 11px;")
        info_layout.addWidget(path_label)
        
        info_layout.addStretch()
        
        count_label = QLabel("文件: 0")
        count_label.setStyleSheet("color: #666666; font-size: 11px;")
        info_layout.addWidget(count_label)
        
        layout.addLayout(info_layout)
        
        # 文件列表
        file_tree = QTreeWidget()
        file_tree.setHeaderLabels(["文件名", "大小", "修改时间"])
        file_tree.setAlternatingRowColors(True)
        file_tree.setRootIsDecorated(True)
        
        # 设置列宽
        header = file_tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(file_tree)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("📋 全选")
        select_all_btn.setMaximumWidth(60)
        btn_layout.addWidget(select_all_btn)
        
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.setMaximumWidth(60)
        btn_layout.addWidget(refresh_btn)
        
        btn_layout.addStretch()
        
        layout.addLayout(btn_layout)
        
        # 保存控件引用
        if dir_type == "dir1":
            self.dir1_tree = file_tree
            self.dir1_path_label = path_label
            self.dir1_count_label = count_label
        else:
            self.dir2_tree = file_tree
            self.dir2_path_label = path_label
            self.dir2_count_label = count_label
        
        return group
    
    def create_comparison_result_widget(self):
        """创建对比结果控件"""
        group = QGroupBox("🔄 对比结果")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.stats_label = QLabel("📊 等待对比...")
        self.stats_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        stats_layout.addWidget(self.stats_label)
        
        stats_layout.addStretch()
        
        # 筛选按钮
        self.filter_same_btn = QPushButton("相同")
        self.filter_same_btn.setCheckable(True)
        self.filter_same_btn.setMaximumWidth(50)
        stats_layout.addWidget(self.filter_same_btn)
        
        self.filter_diff_btn = QPushButton("不同")
        self.filter_diff_btn.setCheckable(True)
        self.filter_diff_btn.setChecked(True)
        self.filter_diff_btn.setMaximumWidth(50)
        stats_layout.addWidget(self.filter_diff_btn)
        
        self.filter_unique_btn = QPushButton("独有")
        self.filter_unique_btn.setCheckable(True)
        self.filter_unique_btn.setChecked(True)
        self.filter_unique_btn.setMaximumWidth(50)
        stats_layout.addWidget(self.filter_unique_btn)
        
        layout.addLayout(stats_layout)
        
        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(5)
        self.result_table.setHorizontalHeaderLabels([
            "文件名", "状态", "目录1", "目录2", "建议操作"
        ])
        
        # 设置表格属性
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 文件名
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)           # 目录1
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)           # 目录2
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 建议操作
        
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.result_table.setSortingEnabled(True)
        
        layout.addWidget(self.result_table)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        self.sync_to_dir1_btn = QPushButton("📥 同步到目录1")
        self.sync_to_dir1_btn.setEnabled(False)
        batch_layout.addWidget(self.sync_to_dir1_btn)
        
        self.sync_to_dir2_btn = QPushButton("📤 同步到目录2")
        self.sync_to_dir2_btn.setEnabled(False)
        batch_layout.addWidget(self.sync_to_dir2_btn)
        
        self.backup_older_btn = QPushButton("📁 备份旧文件")
        self.backup_older_btn.setEnabled(False)
        batch_layout.addWidget(self.backup_older_btn)
        
        batch_layout.addStretch()
        
        self.export_report_btn = QPushButton("📋 导出报告")
        self.export_report_btn.setEnabled(False)
        batch_layout.addWidget(self.export_report_btn)
        
        layout.addLayout(batch_layout)
        
        return group
    
    def create_bottom_widget(self):
        """创建底部控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("📝 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return widget
    
    def connect_signals(self):
        """连接信号"""
        # 控制面板信号
        self.browse_root_btn.clicked.connect(self.browse_root_directory)
        self.scan_btn.clicked.connect(self.scan_directories)
        self.compare_btn.clicked.connect(self.start_comparison)
        self.execute_btn.clicked.connect(self.execute_operations)
        
        # 目录选择信号
        self.dir1_combo.currentTextChanged.connect(self.on_directory_selection_changed)
        self.dir2_combo.currentTextChanged.connect(self.on_directory_selection_changed)
        
        # 筛选按钮信号
        self.filter_same_btn.toggled.connect(self.filter_results)
        self.filter_diff_btn.toggled.connect(self.filter_results)
        self.filter_unique_btn.toggled.connect(self.filter_results)
        
        # 批量操作信号
        self.sync_to_dir1_btn.clicked.connect(lambda: self.sync_directories("dir1"))
        self.sync_to_dir2_btn.clicked.connect(lambda: self.sync_directories("dir2"))
        self.backup_older_btn.clicked.connect(self.backup_older_files)
        self.export_report_btn.clicked.connect(self.export_comparison_report)
    
    def browse_root_directory(self):
        """浏览根目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择根目录", 
            self.root_path_edit.text() or str(Path.home())
        )
        
        if directory:
            self.root_path_edit.setText(directory)
            self.root_path = Path(directory)
            self.update_subdirectory_list()
            self.log_message(f"📁 根目录已设置: {directory}")
    
    def update_subdirectory_list(self):
        """更新子目录列表"""
        if not self.root_path or not self.root_path.exists():
            return
        
        # 清空现有选项
        self.dir1_combo.clear()
        self.dir2_combo.clear()
        
        # 获取子目录
        subdirs = [d.name for d in self.root_path.iterdir() if d.is_dir()]
        
        if subdirs:
            self.dir1_combo.addItems(subdirs)
            self.dir2_combo.addItems(subdirs)
            self.log_message(f"📂 发现 {len(subdirs)} 个子目录")
        else:
            self.log_message("⚠️ 根目录下没有找到子目录")
    
    def on_directory_selection_changed(self):
        """目录选择变化处理"""
        dir1 = self.dir1_combo.currentText()
        dir2 = self.dir2_combo.currentText()
        
        if dir1 and dir2 and dir1 != dir2:
            self.scan_btn.setEnabled(True)
            self.log_message(f"🎯 已选择对比目录: {dir1} ↔ {dir2}")
        else:
            self.scan_btn.setEnabled(False)
            self.compare_btn.setEnabled(False)
    
    def scan_directories(self):
        """扫描目录"""
        self.log_message("🔍 开始扫描目录...")
        self.status_message.emit("正在扫描目录...")
        
        # 这里应该实现实际的目录扫描逻辑
        # 暂时模拟扫描过程
        self.simulate_directory_scan()
    
    def simulate_directory_scan(self):
        """模拟目录扫描"""
        # 模拟扫描结果
        self.dir1_files = ["file1.txt", "file2.doc", "image.jpg"]
        self.dir2_files = ["file1.txt", "file3.pdf", "image.png"]
        
        # 更新UI
        self.dir1_count_label.setText(f"文件: {len(self.dir1_files)}")
        self.dir2_count_label.setText(f"文件: {len(self.dir2_files)}")
        
        self.compare_btn.setEnabled(True)
        self.log_message("✅ 目录扫描完成")
        self.status_message.emit("目录扫描完成")
    
    def start_comparison(self):
        """开始对比"""
        self.log_message("🔄 开始文件对比...")
        self.status_message.emit("正在对比文件...")
        
        # 这里应该实现实际的文件对比逻辑
        self.simulate_comparison()
    
    def simulate_comparison(self):
        """模拟文件对比"""
        # 模拟对比结果
        self.comparison_results = [
            {"name": "file1.txt", "status": "相同", "dir1": "存在", "dir2": "存在", "action": "无需操作"},
            {"name": "file2.doc", "status": "独有", "dir1": "存在", "dir2": "不存在", "action": "复制到目录2"},
            {"name": "file3.pdf", "status": "独有", "dir1": "不存在", "dir2": "存在", "action": "复制到目录1"},
        ]
        
        self.display_comparison_results()
        self.execute_btn.setEnabled(True)
        self.export_report_btn.setEnabled(True)
        
        self.log_message("✅ 文件对比完成")
        self.status_message.emit("文件对比完成")
    
    def display_comparison_results(self):
        """显示对比结果"""
        self.result_table.setRowCount(len(self.comparison_results))
        
        for row, result in enumerate(self.comparison_results):
            self.result_table.setItem(row, 0, QTableWidgetItem(result["name"]))
            self.result_table.setItem(row, 1, QTableWidgetItem(result["status"]))
            self.result_table.setItem(row, 2, QTableWidgetItem(result["dir1"]))
            self.result_table.setItem(row, 3, QTableWidgetItem(result["dir2"]))
            self.result_table.setItem(row, 4, QTableWidgetItem(result["action"]))
        
        self.stats_label.setText(f"📊 对比结果: {len(self.comparison_results)} 项")
    
    def filter_results(self):
        """筛选结果"""
        # 这里应该实现结果筛选逻辑
        self.log_message("🔍 筛选对比结果...")
    
    def sync_directories(self, target_dir):
        """同步目录"""
        self.log_message(f"🔄 开始同步到{target_dir}...")
        self.status_message.emit(f"正在同步到{target_dir}")
    
    def backup_older_files(self):
        """备份旧文件"""
        self.log_message("📁 开始备份旧文件...")
        self.status_message.emit("正在备份旧文件")
    
    def execute_operations(self):
        """执行操作"""
        self.log_message("✅ 开始执行操作...")
        self.status_message.emit("正在执行操作")
    
    def export_comparison_report(self):
        """导出对比报告"""
        self.log_message("📋 导出对比报告...")
        self.status_message.emit("正在导出报告")
    
    def log_message(self, message):
        """记录日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        self.log_text.ensureCursorVisible()
    
    def get_settings(self):
        """获取当前设置"""
        return {
            "root_path": self.root_path_edit.text(),
            "dir1": self.dir1_combo.currentText(),
            "dir2": self.dir2_combo.currentText(),
            "match_mode": self.match_mode_combo.currentText(),
            "time_strategy": self.time_strategy_combo.currentText(),
            "recursive": self.recursive_check.isChecked(),
            "ignore_case": self.ignore_case_check.isChecked(),
            "content_compare": self.content_compare_check.isChecked(),
            "safe_mode": self.safe_mode_check.isChecked()
        }
    
    def load_settings(self, settings):
        """加载设置"""
        if "root_path" in settings:
            self.root_path_edit.setText(settings["root_path"])
        # 可以继续加载其他设置...
    
    def clear_settings(self):
        """清空设置"""
        self.root_path_edit.clear()
        self.dir1_combo.clear()
        self.dir2_combo.clear()
        self.result_table.setRowCount(0)
        self.comparison_results.clear()
        self.log_message("🔄 已清空所有设置")
