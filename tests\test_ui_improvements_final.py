#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI改进最终测试
测试日期范围选择器、SpinBox宽度调整和按钮样式改进
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QScrollArea
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.components import (
        FilterConditionsWidget, AdvancedFilterWidget, NumberedFileFilterWidget
    )
    from file_filter_tool.ui.themes import WindowsTheme, VSTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class UIImprovementsFinalTestWidget(QWidget):
    """UI改进最终测试控件"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "windows"
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("UI改进最终测试 - 日期选择器 & 按钮样式 & 控件宽度")
        self.setMinimumSize(900, 800)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        
        # 说明文字
        info_label = QLabel("""
🎉 UI改进最终测试

本次改进包含以下内容：

📅 日期范围选择器：
• 将"x天内修改"改为"从 x年x月x日 至 x年x月x日"
• 支持日历弹出选择
• 默认范围：30天前至今天

📏 控件宽度优化：
• 时间策略中的数量和间隔SpinBox宽度增加到75-85px
• 所有相关控件宽度协调统一

🎨 按钮样式改进：
• SpinBox和ComboBox按钮宽度增加到22px
• 使用渐变背景提升视觉效果
• 箭头大小增加，更容易识别
• 悬停和按下状态更明显

💡 测试方法：
• 点击日期选择器的下拉按钮
• 测试SpinBox的+/-按钮
• 观察悬停和点击效果
• 切换主题查看不同效果
        """)
        layout.addWidget(info_label)
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.theme_btn = QPushButton("切换到 VS 主题")
        self.theme_btn.clicked.connect(self.switch_theme)
        theme_layout.addWidget(self.theme_btn)
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 测试组1：筛选条件组件（包含新的日期选择器）
        conditions_group = QGroupBox("筛选条件组件 - 新增日期范围选择器")
        conditions_layout = QVBoxLayout(conditions_group)
        
        self.filter_conditions = FilterConditionsWidget()
        conditions_layout.addWidget(self.filter_conditions)
        
        layout.addWidget(conditions_group)
        
        # 测试组2：高级筛选组件
        advanced_group = QGroupBox("高级筛选组件")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.advanced_filter = AdvancedFilterWidget()
        advanced_layout.addWidget(self.advanced_filter)
        
        layout.addWidget(advanced_group)
        
        # 测试组3：编号文件筛选组件（宽度已优化）
        numbered_group = QGroupBox("编号文件筛选组件 - 控件宽度已优化")
        numbered_layout = QVBoxLayout(numbered_group)
        
        self.numbered_filter = NumberedFileFilterWidget()
        numbered_layout.addWidget(self.numbered_filter)
        
        layout.addWidget(numbered_group)
        
        # 测试控制按钮
        control_layout = QHBoxLayout()
        
        test_dates_btn = QPushButton("设置测试日期范围")
        test_dates_btn.clicked.connect(self.set_test_dates)
        control_layout.addWidget(test_dates_btn)
        
        fill_data_btn = QPushButton("填充测试数据")
        fill_data_btn.clicked.connect(self.fill_test_data)
        control_layout.addWidget(fill_data_btn)
        
        clear_btn = QPushButton("清空所有数据")
        clear_btn.clicked.connect(self.clear_all)
        control_layout.addWidget(clear_btn)
        
        get_values_btn = QPushButton("获取当前设置")
        get_values_btn.clicked.connect(self.get_current_values)
        control_layout.addWidget(get_values_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 设置滚动区域
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
    
    def apply_theme(self):
        """应用主题"""
        if self.current_theme == "windows":
            self.setStyleSheet(WindowsTheme.get_complete_style())
        else:
            self.setStyleSheet(VSTheme.get_complete_style())
    
    def switch_theme(self):
        """切换主题"""
        if self.current_theme == "windows":
            self.current_theme = "vs"
            self.theme_btn.setText("切换到 Windows 主题")
            print("🌙 切换到 Visual Studio 深色主题")
        else:
            self.current_theme = "windows"
            self.theme_btn.setText("切换到 VS 主题")
            print("☀️ 切换到 Windows 浅色主题")
        
        self.apply_theme()
    
    def set_test_dates(self):
        """设置测试日期范围"""
        # 设置开始日期为60天前
        start_date = QDate.currentDate().addDays(-60)
        self.filter_conditions.start_date.setDate(start_date)
        
        # 设置结束日期为10天前
        end_date = QDate.currentDate().addDays(-10)
        self.filter_conditions.end_date.setDate(end_date)
        
        print("📅 已设置测试日期范围：60天前 至 10天前")
    
    def fill_test_data(self):
        """填充测试数据"""
        # 筛选条件
        self.filter_conditions.extensions_edit.setText(".txt .py .jpg .pdf")
        self.filter_conditions.pattern_edit.setText("test.*|demo.*")
        self.filter_conditions.min_size_edit.setText("1KB")
        self.filter_conditions.max_size_edit.setText("50MB")
        
        # 高级筛选
        self.advanced_filter.duplicate_check.setChecked(True)
        self.advanced_filter.min_duplicate_spin.setValue(3)
        self.advanced_filter.similar_check.setChecked(True)
        self.advanced_filter.min_common_chars_spin.setValue(8)
        self.advanced_filter.similarity_spin.setValue(85)
        
        # 编号文件筛选
        self.numbered_filter.enable_check.setChecked(True)
        self.numbered_filter.strategy_combo.setCurrentText("保留最新N个")
        self.numbered_filter.count_spin.setValue(5)
        self.numbered_filter.interval_spin.setValue(14)
        self.numbered_filter.ignore_extension_check.setChecked(True)
        self.numbered_filter.case_sensitive_check.setChecked(False)
        
        print("📝 测试数据填充完成")
    
    def clear_all(self):
        """清空所有数据"""
        self.filter_conditions.clear()
        self.advanced_filter.clear()
        self.numbered_filter.clear()
        
        print("🧹 所有数据清空完成")
    
    def get_current_values(self):
        """获取当前设置"""
        print("\n📊 当前设置值：")
        print("=" * 50)
        
        # 筛选条件
        print("📁 筛选条件：")
        print(f"  扩展名: {self.filter_conditions.extensions_edit.text()}")
        print(f"  文件名模式: {self.filter_conditions.pattern_edit.text()}")
        print(f"  最小大小: {self.filter_conditions.min_size_edit.text()}")
        print(f"  最大大小: {self.filter_conditions.max_size_edit.text()}")
        
        start_date, end_date = self.filter_conditions.get_date_range()
        print(f"  日期范围: {start_date} 至 {end_date}")
        
        # 高级筛选
        print("\n🔍 高级筛选：")
        dup_enabled, dup_count = self.advanced_filter.get_duplicate_settings()
        print(f"  重复文件: {dup_enabled}, 最少重复: {dup_count}")
        
        sim_enabled, sim_chars, sim_threshold = self.advanced_filter.get_similar_settings()
        print(f"  相似文件: {sim_enabled}, 相同字符: {sim_chars}, 相似度: {sim_threshold}")
        
        # 编号文件筛选
        print("\n🔢 编号文件筛选：")
        numbered_settings = self.numbered_filter.get_settings()
        if numbered_settings:
            print(f"  启用: {numbered_settings['enabled']}")
            print(f"  策略: {numbered_settings['strategy']}")
            print(f"  选项: {numbered_settings['options']}")
            print(f"  忽略扩展名: {numbered_settings['ignore_extension']}")
            print(f"  区分大小写: {numbered_settings['case_sensitive']}")
        else:
            print("  未启用")

def test_ui_improvements_final():
    """测试UI改进最终版本"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行UI测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🎉 UI改进最终测试启动")
    print("=" * 60)
    print("📋 本次改进内容:")
    print("  • 日期范围选择器：从'x天内修改'改为'从x年x月x日至x年x月x日'")
    print("  • 控件宽度优化：时间策略中的SpinBox宽度增加到75-85px")
    print("  • 按钮样式改进：SpinBox/ComboBox按钮宽度22px，渐变背景")
    print("  • 箭头大小优化：更大更清晰的箭头符号")
    print()
    print("💡 测试重点:")
    print("  • 日期选择器的日历弹出功能")
    print("  • SpinBox按钮的视觉效果和点击体验")
    print("  • ComboBox下拉按钮的样式统一")
    print("  • 控件宽度的协调性")
    print("  • 主题切换时的样式更新")
    print()
    print("🎯 验证方法:")
    print("  • 点击日期选择器查看日历")
    print("  • 测试SpinBox的+/-按钮")
    print("  • 观察悬停和按下效果")
    print("  • 切换主题观察样式变化")
    print("  • 填充测试数据观察控件宽度")
    print()
    
    # 创建测试窗口
    test_widget = UIImprovementsFinalTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行UI测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_ui_improvements_final()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
