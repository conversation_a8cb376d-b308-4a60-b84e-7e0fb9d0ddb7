#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块测试脚本
验证重构后的模块是否正常工作
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

def test_ui_modules():
    """测试UI模块"""
    print("🧪 测试UI模块...")
    
    try:
        from file_filter_tool.ui import VSTheme
        print("✅ VSTheme导入成功")
        
        # 测试主题样式生成
        style = VSTheme.get_complete_style()
        assert len(style) > 1000, "样式表内容太少"
        print("✅ 主题样式生成正常")
        
        # 测试颜色配置
        colors = VSTheme.COLORS
        assert 'background' in colors, "缺少背景色配置"
        assert 'text_primary' in colors, "缺少主文本色配置"
        print("✅ 颜色配置完整")
        
    except Exception as e:
        print(f"❌ UI模块测试失败: {e}")
        return False
    
    return True

def test_core_modules():
    """测试核心模块"""
    print("\n🧪 测试核心模块...")
    
    try:
        from file_filter_tool.core import FileSearchThread, FileOperationThread
        print("✅ 线程类导入成功")
        
        # 测试线程类是否可以实例化（不运行）
        from file_filter_tool.file_filter import FileFilter
        file_filter = FileFilter(".")
        
        search_thread = FileSearchThread(file_filter, extensions=['.py'])
        print("✅ FileSearchThread实例化成功")
        
        operation_thread = FileOperationThread(file_filter, "copy", [], ".")
        print("✅ FileOperationThread实例化成功")
        
    except Exception as e:
        print(f"❌ 核心模块测试失败: {e}")
        return False
    
    return True

def test_main_modules():
    """测试主要模块"""
    print("\n🧪 测试主要模块...")
    
    try:
        from file_filter_tool.file_filter import FileFilter
        print("✅ FileFilter导入成功")
        
        from file_filter_tool.config import ConfigManager, FilterConfig
        print("✅ 配置管理导入成功")
        
        # 测试基本功能
        file_filter = FileFilter(".")
        config_manager = ConfigManager()
        print("✅ 基本类实例化成功")
        
    except Exception as e:
        print(f"❌ 主要模块测试失败: {e}")
        return False
    
    return True

def test_gui_import():
    """测试GUI导入"""
    print("\n🧪 测试GUI导入...")
    
    try:
        # 测试是否可以导入GUI主窗口
        from file_filter_tool.gui_main import FileFilterMainWindow
        print("✅ 主窗口类导入成功")
        
        # 测试PySide6是否可用
        try:
            from PySide6.QtWidgets import QApplication
            print("✅ PySide6可用")
        except ImportError:
            print("⚠️ PySide6不可用，但模块结构正常")
        
    except Exception as e:
        print(f"❌ GUI导入测试失败: {e}")
        return False
    
    return True

def test_package_structure():
    """测试包结构"""
    print("\n🧪 测试包结构...")
    
    try:
        import file_filter_tool
        print("✅ 主包导入成功")
        
        # 测试子包
        import file_filter_tool.ui
        import file_filter_tool.core
        print("✅ 子包导入成功")
        
        # 测试__all__导出
        from file_filter_tool import FileFilter, ConfigManager
        print("✅ 主要类导出正常")
        
    except Exception as e:
        print(f"❌ 包结构测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始模块测试...\n")
    
    tests = [
        test_package_structure,
        test_main_modules,
        test_ui_modules,
        test_core_modules,
        test_gui_import,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("💥 测试中断")
            break
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有模块测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要检查模块结构")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
