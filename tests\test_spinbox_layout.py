#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpinBox上下布局测试
测试最少重复、相同字符、相似度、数量、间隔等SpinBox的上下布局
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QSpinBox, QComboBox, QDateEdit
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.themes import WindowsTheme
    from file_filter_tool.ui.components import AdvancedFilterWidget, NumberedFileFilterWidget
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class SpinBoxLayoutTestWidget(QWidget):
    """SpinBox上下布局测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("SpinBox上下布局测试 - 系统默认箭头")
        self.setMinimumSize(900, 700)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🔄 SpinBox上下布局测试

本测试验证以下SpinBox控件的箭头布局从左右改为上下：

🎯 测试的SpinBox控件：
• 最少重复：高级筛选中的重复文件数量
• 相同字符：高级筛选中的相似文件名字符数
• 相似度：高级筛选中的文件名相似度百分比
• 数量：编号文件筛选中的保留数量
• 间隔：编号文件筛选中的间隔天数

✅ 上下布局特点：
• 增加按钮（▲）：位于右上角
• 减少按钮（▼）：位于右下角
• 按钮尺寸：20px宽 × 12px高
• 系统默认箭头：清晰可见
• 布局逻辑：符合增加/减少的直觉

🔍 测试重点：
• 所有SpinBox是否都是上下布局
• 箭头是否清晰可见
• 按钮功能是否正常工作
• 布局是否美观协调
        """)
        layout.addWidget(info_label)
        
        # 测试组1：高级筛选组件
        advanced_group = QGroupBox("高级筛选组件 - SpinBox上下布局测试")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.advanced_filter = AdvancedFilterWidget()
        advanced_layout.addWidget(self.advanced_filter)
        
        # 添加说明
        advanced_info = QLabel("""
📊 高级筛选中的SpinBox控件：
• 最少重复：设置重复文件的最少数量（2-999个）
• 相同字符：设置文件名相同字符的最少数量（3-50个）
• 相似度：设置文件名相似度阈值（50-100%）

🔍 测试方法：点击各个SpinBox的▲▼按钮，观察布局和功能
        """)
        advanced_info.setStyleSheet("color: #666666; font-style: italic; padding: 5px;")
        advanced_layout.addWidget(advanced_info)
        
        layout.addWidget(advanced_group)
        
        # 测试组2：编号文件筛选组件
        numbered_group = QGroupBox("编号文件筛选组件 - SpinBox上下布局测试")
        numbered_layout = QVBoxLayout(numbered_group)
        
        self.numbered_filter = NumberedFileFilterWidget()
        numbered_layout.addWidget(self.numbered_filter)
        
        # 添加说明
        numbered_info = QLabel("""
📝 编号文件筛选中的SpinBox控件：
• 数量：设置保留文件的数量（1-100个）
• 间隔：设置按间隔保留的天数（1-365天）

🔍 测试方法：
1. 勾选"启用编号文件时间筛选"
2. 选择不同的策略（如"保留最新N个"、"按间隔保留"）
3. 测试数量和间隔SpinBox的▲▼按钮
        """)
        numbered_info.setStyleSheet("color: #666666; font-style: italic; padding: 5px;")
        numbered_layout.addWidget(numbered_info)
        
        layout.addWidget(numbered_group)
        
        # 测试组3：对比测试
        compare_group = QGroupBox("对比测试 - 不同SpinBox控件")
        compare_layout = QVBoxLayout(compare_group)
        
        # 创建各种SpinBox进行对比
        spinbox_tests = [
            ("基本SpinBox", 1, 100, 10, ""),
            ("带后缀SpinBox", 1, 999, 5, " 个"),
            ("百分比SpinBox", 0, 100, 70, " %"),
            ("天数SpinBox", 1, 365, 7, " 天"),
            ("大范围SpinBox", 0, 9999, 100, ""),
        ]
        
        for label_text, min_val, max_val, default_val, suffix in spinbox_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(100)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            if suffix:
                spinbox.setSuffix(suffix)
            spinbox.setMinimumWidth(80)
            spinbox.setMaximumWidth(100)
            row_layout.addWidget(spinbox)
            
            # 添加说明
            desc_label = QLabel("← 上下布局▲▼，系统默认箭头")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            compare_layout.addLayout(row_layout)
        
        layout.addWidget(compare_group)
        
        # 测试说明
        test_info = QLabel("""
🔄 SpinBox上下布局改进总结：

✅ 改进的控件：
• 高级筛选 → 最少重复SpinBox：2-999个
• 高级筛选 → 相同字符SpinBox：3-50个  
• 高级筛选 → 相似度SpinBox：50-100%
• 编号文件筛选 → 数量SpinBox：1-100个
• 编号文件筛选 → 间隔SpinBox：1-365天

🎯 布局特点：
• 增加按钮（▲）：位于控件右上角
• 减少按钮（▼）：位于控件右下角
• 按钮尺寸：20px宽 × 12px高
• 系统默认：使用Qt原生箭头样式
• 清晰可见：无自定义样式，兼容性最佳

🔍 测试验证：
1. 观察所有SpinBox是否都是上下布局
2. 点击▲▼按钮测试功能是否正常
3. 检查箭头是否清晰可见
4. 验证布局是否美观协调
5. 确认与其他控件的视觉一致性

💡 优势：
• 布局直观：上下布局符合增加/减少的逻辑
• 兼容性好：系统默认样式，无兼容性问题
• 清晰度高：原生箭头，显示效果最佳
• 维护简单：无复杂自定义样式
        """)
        test_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(test_info)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(WindowsTheme.get_complete_style())

def test_spinbox_layout():
    """测试SpinBox上下布局"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行SpinBox布局测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🔄 SpinBox上下布局测试启动")
    print("=" * 50)
    print("🎯 测试的SpinBox控件:")
    print("  • 最少重复：高级筛选中的重复文件数量")
    print("  • 相同字符：高级筛选中的相似文件名字符数")
    print("  • 相似度：高级筛选中的文件名相似度百分比")
    print("  • 数量：编号文件筛选中的保留数量")
    print("  • 间隔：编号文件筛选中的间隔天数")
    print()
    print("✅ 上下布局特点:")
    print("  • 增加按钮（▲）：位于右上角")
    print("  • 减少按钮（▼）：位于右下角")
    print("  • 按钮尺寸：20px宽 × 12px高")
    print("  • 系统默认箭头：清晰可见")
    print("  • 布局逻辑：符合增加/减少的直觉")
    print()
    print("🔍 测试重点:")
    print("  • 所有SpinBox是否都是上下布局")
    print("  • 箭头是否清晰可见")
    print("  • 按钮功能是否正常工作")
    print("  • 布局是否美观协调")
    print("  • 与其他控件的视觉一致性")
    print()
    print("💡 测试方法:")
    print("  • 观察高级筛选组件中的3个SpinBox")
    print("  • 启用编号文件筛选，测试数量和间隔SpinBox")
    print("  • 点击各个▲▼按钮验证功能")
    print("  • 对比不同SpinBox的布局效果")
    print()
    
    # 创建测试窗口
    test_widget = SpinBoxLayoutTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行SpinBox布局测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_spinbox_layout()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
