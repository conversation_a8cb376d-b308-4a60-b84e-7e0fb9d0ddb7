#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - GUI启动器
自动检测并启动最佳的图形界面版本
包含详细的依赖检测和错误处理
"""

import sys
import os
import traceback
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    print("✅ Python版本检查通过")
    return True

def check_file_exists(filename):
    """检查文件是否存在"""
    if Path(filename).exists():
        print(f"✅ 找到文件: {filename}")
        return True
    else:
        print(f"❌ 文件不存在: {filename}")
        return False

def check_pyside6():
    """检查PySide6是否可用"""
    print("\n🔍 检查PySide6依赖...")
    try:
        import PySide6
        print(f"✅ PySide6版本: {PySide6.__version__}")

        # 检查具体模块
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QIcon
        print("✅ PySide6核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ PySide6不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ PySide6检查失败: {e}")
        return False

def check_tkinter():
    """检查tkinter是否可用"""
    print("\n🔍 检查tkinter依赖...")
    try:
        import tkinter as tk
        print(f"✅ tkinter版本: {tk.TkVersion}")

        # 尝试创建一个测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✅ tkinter测试窗口创建成功")
        return True
    except ImportError as e:
        print(f"❌ tkinter不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ tkinter检查失败: {e}")
        return False

def install_pyside6():
    """尝试安装PySide6"""
    print("\n📦 尝试安装PySide6...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "PySide6"
        ], capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("✅ PySide6安装成功")
            return True
        else:
            print(f"❌ PySide6安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ 安装超时")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def try_launch_pyside6():
    """尝试启动PySide6版本"""
    print("\n🚀 启动PySide6版本...")
    try:
        # 检查文件是否存在
        if not check_file_exists("gui_pyside6.py"):
            return False

        # 导入并启动
        from gui_pyside6 import main as pyside6_main
        print("✅ PySide6模块导入成功")
        pyside6_main()
        return True
    except ImportError as e:
        print(f"❌ PySide6导入失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"❌ PySide6启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def try_launch_tkinter():
    """尝试启动tkinter版本"""
    print("\n🚀 启动tkinter版本...")
    try:
        # 检查文件是否存在
        if not check_file_exists("gui.py"):
            return False

        # 导入并启动
        from gui import main as tkinter_main
        print("✅ tkinter模块导入成功")
        tkinter_main()
        return True
    except ImportError as e:
        print(f"❌ tkinter导入失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"❌ tkinter启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def show_help():
    """显示帮助信息"""
    print("\n" + "="*50)
    print("📋 故障排除指南")
    print("="*50)
    print("\n1. 安装PySide6 (推荐):")
    print("   pip install PySide6")
    print("\n2. 如果pip安装失败，尝试:")
    print("   pip install --user PySide6")
    print("   或")
    print("   python -m pip install PySide6")
    print("\n3. 检查Python版本 (需要3.6+):")
    print("   python --version")
    print("\n4. 使用命令行版本:")
    print("   python main.py --help")
    print("\n5. 如果在虚拟环境中，确保激活了正确的环境")
    print("\n6. 在某些Linux系统上可能需要安装:")
    print("   sudo apt-get install python3-tk")
    print("   sudo apt-get install python3-pyside6")

def main():
    """主函数 - 启动GUI"""
    print("🎯 文件筛选过滤工具 - GUI启动器")
    print("=" * 50)

    # 检查Python版本
    if not check_python_version():
        input("\n按回车键退出...")
        return

    # 检查当前目录
    print(f"\n📁 当前工作目录: {os.getcwd()}")

    # 检查核心文件
    print("\n📋 检查核心文件...")
    core_files = ["file_filter.py", "config.py"]
    missing_files = []
    for file in core_files:
        if not check_file_exists(file):
            missing_files.append(file)

    if missing_files:
        print(f"\n❌ 缺少核心文件: {missing_files}")
        print("请确保在正确的项目目录中运行此脚本")
        input("\n按回车键退出...")
        return

    # 尝试PySide6版本
    pyside6_available = check_pyside6()
    if pyside6_available:
        if try_launch_pyside6():
            return  # 成功启动
    else:
        # 询问是否安装PySide6
        print("\n❓ PySide6未安装，是否尝试自动安装？(y/N): ", end="")
        try:
            response = input().lower().strip()
            if response == 'y':
                if install_pyside6():
                    if check_pyside6() and try_launch_pyside6():
                        return  # 安装并启动成功
        except KeyboardInterrupt:
            print("\n用户取消操作")

    # 尝试tkinter版本
    tkinter_available = check_tkinter()
    if tkinter_available:
        if try_launch_tkinter():
            return  # 成功启动

    # 如果都失败了
    print("\n❌ 无法启动任何图形界面版本")
    show_help()

    input("\n按回车键退出...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        input("\n按回车键退出...")
