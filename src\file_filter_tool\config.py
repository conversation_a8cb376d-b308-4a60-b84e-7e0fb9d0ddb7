#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
支持从配置文件加载筛选规则
"""

import json
from pathlib import Path

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict

@dataclass
class FilterConfig:
    """筛选配置类"""
    name: str = "默认配置"
    base_path: str = "."
    recursive: bool = True
    extensions: Optional[List[str]] = None
    name_pattern: Optional[str] = None
    min_size: Optional[int] = None
    max_size: Optional[int] = None
    modified_days: Optional[int] = None
    action: str = "list"  # list, copy, move, delete
    destination: Optional[str] = None
    confirm_delete: bool = True
    show_details: bool = False

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "filter_config.json"):
        self.config_file = Path(config_file)
        self.configs: Dict[str, FilterConfig] = {}
        self.load_configs()
    
    def load_configs(self) -> None:
        """从配置文件加载配置"""
        if not self.config_file.exists():
            self.create_default_config()
            return
        
        try:
            if self.config_file.suffix.lower() == '.json':
                self._load_json()
            elif self.config_file.suffix.lower() in ['.yml', '.yaml']:
                self._load_yaml()
            else:
                raise ValueError(f"不支持的配置文件格式: {self.config_file.suffix}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.create_default_config()
    
    def _load_json(self) -> None:
        """加载JSON配置文件"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for name, config_data in data.items():
            self.configs[name] = FilterConfig(**config_data)
    
    def _load_yaml(self) -> None:
        """加载YAML配置文件"""
        if not HAS_YAML:
            raise ValueError("需要安装PyYAML包才能加载YAML配置文件")

        with open(self.config_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)

        for name, config_data in data.items():
            self.configs[name] = FilterConfig(**config_data)
    
    def save_configs(self) -> None:
        """保存配置到文件"""
        data = {}
        for name, config in self.configs.items():
            data[name] = asdict(config)
        
        try:
            if self.config_file.suffix.lower() == '.json':
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            elif self.config_file.suffix.lower() in ['.yml', '.yaml']:
                if not HAS_YAML:
                    raise ValueError("需要安装PyYAML包才能保存YAML配置文件")
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def create_default_config(self) -> None:
        """创建默认配置"""
        self.configs = {
            "清理日志文件": FilterConfig(
                name="清理日志文件",
                base_path=".",
                recursive=True,
                extensions=[".log", ".tmp"],
                modified_days=30,
                action="delete",
                confirm_delete=True
            ),
            "图片文件整理": FilterConfig(
                name="图片文件整理",
                base_path=".",
                recursive=True,
                extensions=[".jpg", ".jpeg", ".png", ".gif", ".bmp"],
                action="copy",
                destination="./images/"
            ),
            "大文件查找": FilterConfig(
                name="大文件查找",
                base_path=".",
                recursive=True,
                min_size=100 * 1024 * 1024,  # 100MB
                action="list",
                show_details=True
            ),
            "Python文件备份": FilterConfig(
                name="Python文件备份",
                base_path=".",
                recursive=True,
                extensions=[".py"],
                action="copy",
                destination="./python_backup/"
            )
        }
        self.save_configs()
    
    def get_config(self, name: str) -> Optional[FilterConfig]:
        """获取指定名称的配置"""
        return self.configs.get(name)
    
    def list_configs(self) -> List[str]:
        """列出所有配置名称"""
        return list(self.configs.keys())
    
    def add_config(self, config: FilterConfig) -> None:
        """添加新配置"""
        self.configs[config.name] = config
        self.save_configs()
    
    def remove_config(self, name: str) -> bool:
        """删除配置"""
        if name in self.configs:
            del self.configs[name]
            self.save_configs()
            return True
        return False

def create_sample_configs():
    """创建示例配置文件"""
    manager = ConfigManager("sample_config.json")
    
    # 添加更多示例配置
    configs = [
        FilterConfig(
            name="清理临时文件",
            base_path="C:/temp",
            recursive=True,
            extensions=[".tmp", ".temp", ".cache"],
            action="delete",
            confirm_delete=True
        ),
        FilterConfig(
            name="音频文件整理",
            base_path="./Downloads",
            recursive=True,
            extensions=[".mp3", ".wav", ".flac", ".m4a"],
            action="move",
            destination="./Music/"
        ),
        FilterConfig(
            name="视频文件查找",
            base_path=".",
            recursive=True,
            extensions=[".mp4", ".avi", ".mkv", ".mov"],
            min_size=50 * 1024 * 1024,  # 50MB以上
            action="list",
            show_details=True
        ),
        FilterConfig(
            name="文档备份",
            base_path="./Documents",
            recursive=True,
            extensions=[".doc", ".docx", ".pdf", ".txt"],
            modified_days=7,  # 最近7天修改的
            action="copy",
            destination="./backup/documents/"
        )
    ]
    
    for config in configs:
        manager.add_config(config)
    
    print(f"示例配置文件已创建: {manager.config_file}")

if __name__ == '__main__':
    create_sample_configs()
