#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文件管理工具 v2.0 - 标签页界面版本启动脚本
使用全新的标签页界面设计
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("🚀 启动智能文件管理工具 v2.0 - 标签页界面版本")
    print("=" * 60)
    
    try:
        # 检查PySide6是否可用
        try:
            import PySide6
            print("✅ PySide6 已安装")
        except ImportError:
            print("❌ PySide6 未安装")
            print("请运行以下命令安装：")
            print("pip install PySide6")
            return False
        
        # 导入并启动新的标签页主窗口
        from file_filter_tool.gui_tabbed_main import main as tabbed_main
        
        print("🎨 使用全新的标签页界面设计")
        print("📋 功能模块：")
        print("  🔍 普通搜索 - 强大的文件搜索和筛选")
        print("  🔄 双目录对比 - 专业的分屏对比界面")
        print("  🗂️ 重复清理 - 智能重复文件检测和清理")
        print("  📊 批量操作 - 批量重命名、转换、分析、整理")
        print()
        print("⌨️ 快捷键：")
        print("  Ctrl+1/2/3/4 - 切换标签页")
        print("  Ctrl+N - 新建项目")
        print("  Ctrl+I/E - 导入/导出设置")
        print("  F1 - 帮助")
        print()
        print("🎯 正在启动界面...")
        
        # 启动应用
        result = tabbed_main()
        
        if result:
            print("✅ 应用程序正常退出")
        else:
            print("❌ 应用程序异常退出")
        
        return result
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖都已正确安装")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 程序崩溃: {e}")
        sys.exit(1)
