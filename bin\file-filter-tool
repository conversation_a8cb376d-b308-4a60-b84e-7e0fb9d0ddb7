#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选过滤工具 - 主启动脚本
File Filter Tool - Main Launcher Script
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    try:
        # 启动重构后的PySide6版本
        from file_filter_tool.gui_main import main as gui_main
        gui_main()
    except ImportError as e:
        print("错误：无法导入PySide6模块")
        print("请确保已安装PySide6：pip install PySide6")
        print(f"详细错误：{e}")
        return False
    except Exception as e:
        print(f"启动失败：{e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
