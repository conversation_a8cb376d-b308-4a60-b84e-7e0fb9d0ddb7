"""
文件筛选工具包

一个功能强大的文件筛选和管理工具，支持：
- 多种筛选条件（扩展名、大小、时间、名称模式）
- 重复文件检测（基于MD5哈希值）
- 文件名相似度检测（基于字符匹配和模糊匹配）
- 文件操作（复制、移动、删除）
- 配置管理（保存和加载筛选规则）
- 现代化的图形界面（基于PySide6/Qt6）
"""

__version__ = "1.0.0"
__author__ = "Andy_127【浅醉丶墨语】"
__email__ = "yangjun_127"

from .file_filter import FileFilter
from .config import ConfigManager, FilterConfig

# 导入新的模块
from . import ui
from . import core

__all__ = [
    "FileFilter",
    "ConfigManager",
    "FilterConfig",
    "ui",
    "core",
    "__version__",
    "__author__",
    "__email__"
]
