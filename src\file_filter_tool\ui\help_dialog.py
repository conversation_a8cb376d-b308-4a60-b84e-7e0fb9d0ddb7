#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用帮助对话框
提供详细的功能说明和使用方法
"""

try:
    from PySide6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, 
        QPushButton, QTabWidget, QWidget, QLabel
    )
    from PySide6.QtCore import Qt
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

class HelpDialog(QDialog):
    """使用帮助对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("使用帮助 - 文件筛选过滤工具")
        self.setMinimumSize(800, 600)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 功能说明标签页
        functions_tab = self.create_functions_tab()
        tab_widget.addTab(functions_tab, "功能说明")
        
        # 使用方法标签页
        usage_tab = self.create_usage_tab()
        tab_widget.addTab(usage_tab, "使用方法")
        
        # 快捷键标签页
        shortcuts_tab = self.create_shortcuts_tab()
        tab_widget.addTab(shortcuts_tab, "快捷键")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        close_btn.setMinimumWidth(80)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def create_functions_tab(self):
        """创建功能说明标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>🔍 文件筛选过滤工具 - 功能说明</h2>
        
        <h3>📁 搜索路径</h3>
        <ul>
            <li><b>搜索目录输入框</b>：输入或选择要搜索的文件夹路径</li>
            <li><b>浏览按钮</b>：点击弹出文件夹选择对话框</li>
            <li><b>递归搜索子目录</b>：勾选后会搜索所有子文件夹</li>
        </ul>
        
        <h3>🎯 筛选条件</h3>
        <ul>
            <li><b>文件扩展名</b>：输入文件类型，如 .txt .py .jpg（空格分隔）</li>
            <li><b>文件名模式</b>：支持正则表达式的文件名匹配</li>
            <li><b>文件大小</b>：设置最小和最大文件大小，支持 KB、MB、GB</li>
            <li><b>修改时间</b>：选择文件修改时间范围，从开始日期到结束日期</li>
        </ul>
        
        <h3>🔍 高级筛选</h3>
        <ul>
            <li><b>重复文件筛选</b>：查找相同内容的重复文件</li>
            <li><b>最少重复数量</b>：设置至少重复多少次才显示</li>
            <li><b>相似文件名筛选</b>：查找文件名相似的文件</li>
            <li><b>相同字符数</b>：设置文件名中相同字符的最少数量</li>
        </ul>
        
        <h3>📊 编号文件时间筛选</h3>
        <ul>
            <li><b>启用编号文件时间筛选</b>：对带编号的文件进行时间筛选</li>
            <li><b>策略选择</b>：保留最新、最旧、最新N个、最旧N个、时间范围、按间隔保留</li>
            <li><b>数量设置</b>：当选择保留N个时，设置具体数量</li>
            <li><b>间隔设置</b>：当选择按间隔保留时，设置间隔天数</li>
            <li><b>忽略扩展名</b>：比较文件名时是否忽略扩展名</li>
            <li><b>区分大小写</b>：比较文件名时是否区分大小写</li>
        </ul>
        
        <h3>⚡ 操作按钮</h3>
        <ul>
            <li><b>开始搜索</b>：根据设置的条件开始搜索文件</li>
            <li><b>清空条件</b>：清除所有筛选条件，恢复默认设置</li>
            <li><b>导出结果</b>：将搜索结果导出为CSV文件</li>
            <li><b>删除选中</b>：删除在结果列表中选中的文件</li>
            <li><b>移动选中</b>：将选中的文件移动到指定文件夹</li>
            <li><b>复制选中</b>：将选中的文件复制到指定文件夹</li>
        </ul>
        
        <h3>📋 结果显示</h3>
        <ul>
            <li><b>文件列表</b>：显示搜索到的文件，包含文件名、路径、大小、修改时间</li>
            <li><b>多选功能</b>：可以选择多个文件进行批量操作</li>
            <li><b>排序功能</b>：点击列标题可以按该列排序</li>
            <li><b>右键菜单</b>：右键点击文件可以打开、删除、移动等操作</li>
        </ul>
        
        <h3>📝 日志区域</h3>
        <ul>
            <li><b>操作日志</b>：显示所有操作的详细信息</li>
            <li><b>错误提示</b>：显示操作过程中的错误和警告</li>
            <li><b>进度信息</b>：显示搜索和操作的进度</li>
            <li><b>统计信息</b>：显示找到的文件数量等统计数据</li>
        </ul>
        """)
        
        layout.addWidget(text_edit)
        return widget
    
    def create_usage_tab(self):
        """创建使用方法标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>📖 详细使用方法</h2>
        
        <h3>🚀 快速开始</h3>
        <ol>
            <li><b>选择搜索目录</b>：点击"浏览"按钮选择要搜索的文件夹</li>
            <li><b>设置筛选条件</b>：根据需要设置文件类型、大小、时间等条件</li>
            <li><b>开始搜索</b>：点击"开始搜索"按钮执行搜索</li>
            <li><b>查看结果</b>：在结果列表中查看找到的文件</li>
            <li><b>执行操作</b>：选择文件后可以删除、移动、复制或导出</li>
        </ol>
        
        <h3>🎯 常用场景</h3>
        
        <h4>📸 清理重复图片</h4>
        <ol>
            <li>选择图片文件夹</li>
            <li>在文件扩展名中输入：.jpg .jpeg .png .gif</li>
            <li>勾选"查找重复文件"</li>
            <li>点击"开始搜索"</li>
            <li>在结果中选择要删除的重复文件</li>
            <li>点击"删除选中"</li>
        </ol>
        
        <h4>📄 查找大文件</h4>
        <ol>
            <li>选择要检查的目录</li>
            <li>在文件大小的"最小"框中输入：100MB</li>
            <li>勾选"递归搜索子目录"</li>
            <li>点击"开始搜索"</li>
            <li>查看结果列表中的大文件</li>
        </ol>
        
        <h4>🗂️ 清理旧文件</h4>
        <ol>
            <li>选择要清理的目录</li>
            <li>在修改时间中设置结束日期为30天前</li>
            <li>点击"开始搜索"</li>
            <li>选择要删除的旧文件</li>
            <li>点击"删除选中"或"移动选中"到备份文件夹</li>
        </ol>
        
        <h4>📝 整理编号文件</h4>
        <ol>
            <li>选择包含编号文件的目录</li>
            <li>勾选"启用编号文件时间筛选"</li>
            <li>选择策略，如"保留最新N个"</li>
            <li>设置要保留的数量</li>
            <li>点击"开始搜索"</li>
            <li>查看筛选后的结果</li>
        </ol>
        
        <h3>💡 使用技巧</h3>
        
        <h4>🔍 文件名模式匹配</h4>
        <ul>
            <li><b>通配符</b>：使用 * 匹配任意字符，如 *.tmp</li>
            <li><b>正则表达式</b>：如 \\d{4} 匹配4位数字</li>
            <li><b>大小写</b>：默认不区分大小写</li>
        </ul>
        
        <h4>📏 文件大小格式</h4>
        <ul>
            <li><b>字节</b>：直接输入数字，如 1024</li>
            <li><b>KB</b>：如 100KB 或 100 KB</li>
            <li><b>MB</b>：如 50MB 或 50 MB</li>
            <li><b>GB</b>：如 2GB 或 2 GB</li>
        </ul>
        
        <h4>📅 日期选择技巧</h4>
        <ul>
            <li><b>日历选择</b>：点击下拉箭头弹出日历</li>
            <li><b>快速输入</b>：直接在输入框中修改日期</li>
            <li><b>范围设置</b>：开始日期不能晚于结束日期</li>
        </ul>
        
        <h3>⚠️ 注意事项</h3>
        <ul>
            <li><b>备份重要文件</b>：删除操作不可恢复，请先备份重要文件</li>
            <li><b>权限问题</b>：某些系统文件夹可能需要管理员权限</li>
            <li><b>大目录搜索</b>：搜索大目录时可能需要较长时间</li>
            <li><b>网络驱动器</b>：网络驱动器的搜索速度可能较慢</li>
            <li><b>文件占用</b>：正在使用的文件可能无法删除或移动</li>
        </ul>
        
        <h3>🔧 故障排除</h3>
        <ul>
            <li><b>搜索无结果</b>：检查筛选条件是否过于严格</li>
            <li><b>操作失败</b>：检查文件是否被其他程序占用</li>
            <li><b>权限错误</b>：尝试以管理员身份运行程序</li>
            <li><b>程序卡顿</b>：大量文件操作时请耐心等待</li>
        </ul>
        """)
        
        layout.addWidget(text_edit)
        return widget
    
    def create_shortcuts_tab(self):
        """创建快捷键标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>⌨️ 快捷键说明</h2>
        
        <h3>🔍 搜索操作</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><th>快捷键</th><th>功能</th><th>说明</th></tr>
            <tr><td><b>Ctrl + F</b></td><td>开始搜索</td><td>执行文件搜索操作</td></tr>
            <tr><td><b>Ctrl + R</b></td><td>清空条件</td><td>清除所有筛选条件</td></tr>
            <tr><td><b>F5</b></td><td>刷新</td><td>重新执行上次搜索</td></tr>
        </table>
        
        <h3>📁 文件操作</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><th>快捷键</th><th>功能</th><th>说明</th></tr>
            <tr><td><b>Ctrl + A</b></td><td>全选</td><td>选择结果列表中的所有文件</td></tr>
            <tr><td><b>Ctrl + D</b></td><td>删除选中</td><td>删除选中的文件</td></tr>
            <tr><td><b>Ctrl + M</b></td><td>移动选中</td><td>移动选中的文件到其他位置</td></tr>
            <tr><td><b>Ctrl + C</b></td><td>复制选中</td><td>复制选中的文件到其他位置</td></tr>
            <tr><td><b>Ctrl + E</b></td><td>导出结果</td><td>将搜索结果导出为CSV文件</td></tr>
            <tr><td><b>Delete</b></td><td>删除</td><td>删除当前选中的文件</td></tr>
            <tr><td><b>Enter</b></td><td>打开文件</td><td>用默认程序打开选中的文件</td></tr>
        </table>
        
        <h3>🖱️ 鼠标操作</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><th>操作</th><th>功能</th><th>说明</th></tr>
            <tr><td><b>单击</b></td><td>选择文件</td><td>选择单个文件</td></tr>
            <tr><td><b>Ctrl + 单击</b></td><td>多选</td><td>添加或取消选择文件</td></tr>
            <tr><td><b>Shift + 单击</b></td><td>范围选择</td><td>选择从上次选择到当前位置的所有文件</td></tr>
            <tr><td><b>双击</b></td><td>打开文件</td><td>用默认程序打开文件</td></tr>
            <tr><td><b>右键单击</b></td><td>上下文菜单</td><td>显示文件操作菜单</td></tr>
        </table>
        
        <h3>🎛️ 界面操作</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><th>快捷键</th><th>功能</th><th>说明</th></tr>
            <tr><td><b>F1</b></td><td>使用帮助</td><td>打开本帮助对话框</td></tr>
            <tr><td><b>Ctrl + Q</b></td><td>退出程序</td><td>关闭应用程序</td></tr>
            <tr><td><b>Ctrl + O</b></td><td>选择目录</td><td>打开文件夹选择对话框</td></tr>
            <tr><td><b>Esc</b></td><td>取消操作</td><td>取消当前正在进行的操作</td></tr>
        </table>
        
        <h3>📋 列表操作</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><th>操作</th><th>功能</th><th>说明</th></tr>
            <tr><td><b>点击列标题</b></td><td>排序</td><td>按该列进行升序或降序排序</td></tr>
            <tr><td><b>拖拽列边界</b></td><td>调整列宽</td><td>改变列的显示宽度</td></tr>
            <tr><td><b>上下箭头键</b></td><td>导航</td><td>在文件列表中上下移动选择</td></tr>
            <tr><td><b>Page Up/Down</b></td><td>翻页</td><td>快速浏览大量文件</td></tr>
            <tr><td><b>Home/End</b></td><td>首尾</td><td>跳转到列表的开始或结束</td></tr>
        </table>
        
        <h3>💡 提示</h3>
        <ul>
            <li><b>组合使用</b>：可以组合使用多个快捷键提高效率</li>
            <li><b>上下文相关</b>：某些快捷键只在特定界面元素获得焦点时有效</li>
            <li><b>自定义</b>：部分快捷键可能因系统设置而有所不同</li>
            <li><b>学习建议</b>：建议先熟悉常用的快捷键，逐步掌握更多功能</li>
        </ul>
        """)
        
        layout.addWidget(text_edit)
        return widget
