#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpinBox按钮间距测试
测试SpinBox按钮的2px间距效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QSpinBox, QComboBox, QDateEdit
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.themes import WindowsTheme
    from file_filter_tool.ui.components import AdvancedFilterWidget, NumberedFileFilterWidget
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class SpinBoxMarginTestWidget(QWidget):
    """SpinBox按钮间距测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("SpinBox按钮间距测试 - 2px间距")
        self.setMinimumSize(900, 700)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
📐 SpinBox按钮间距测试

本测试验证SpinBox按钮的2px间距效果：

🎯 间距设置：
• 上按钮（▲）：right: 2px, top: 2px
• 下按钮（▼）：right: 2px, bottom: 2px
• 按钮尺寸：20px宽 × 12px高
• 布局位置：上下垂直排列

✅ 预期效果：
• 按钮不再紧贴边框
• 上下左右都有2px的间距
• 视觉效果更加协调
• 点击区域仍然足够大

🔍 测试重点：
• 按钮位置是否合适
• 间距是否均匀
• 视觉效果是否美观
• 功能是否正常工作
        """)
        layout.addWidget(info_label)
        
        # 测试组1：实际组件测试
        real_group = QGroupBox("实际组件测试 - 高级筛选和编号文件筛选")
        real_layout = QVBoxLayout(real_group)
        
        # 高级筛选组件
        advanced_label = QLabel("高级筛选组件：")
        advanced_label.setStyleSheet("font-weight: bold; color: #0078d4;")
        real_layout.addWidget(advanced_label)
        
        self.advanced_filter = AdvancedFilterWidget()
        real_layout.addWidget(self.advanced_filter)
        
        # 编号文件筛选组件
        numbered_label = QLabel("编号文件筛选组件：")
        numbered_label.setStyleSheet("font-weight: bold; color: #0078d4; margin-top: 10px;")
        real_layout.addWidget(numbered_label)
        
        self.numbered_filter = NumberedFileFilterWidget()
        real_layout.addWidget(self.numbered_filter)
        
        layout.addWidget(real_group)
        
        # 测试组2：不同尺寸的SpinBox对比
        size_group = QGroupBox("不同尺寸SpinBox对比测试")
        size_layout = QVBoxLayout(size_group)
        
        size_tests = [
            ("小尺寸SpinBox", 60, 80, 1, 100, 10, ""),
            ("中等尺寸SpinBox", 80, 100, 1, 999, 50, " 个"),
            ("大尺寸SpinBox", 100, 120, 0, 100, 75, " %"),
            ("超大尺寸SpinBox", 120, 150, 1, 365, 30, " 天"),
        ]
        
        for label_text, min_width, max_width, min_val, max_val, default_val, suffix in size_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(120)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            if suffix:
                spinbox.setSuffix(suffix)
            spinbox.setMinimumWidth(min_width)
            spinbox.setMaximumWidth(max_width)
            row_layout.addWidget(spinbox)
            
            # 添加说明
            desc_label = QLabel(f"← {min_width}-{max_width}px宽度，2px间距")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            size_layout.addLayout(row_layout)
        
        layout.addWidget(size_group)
        
        # 测试组3：按钮状态测试
        state_group = QGroupBox("按钮状态和交互测试")
        state_layout = QVBoxLayout(state_group)
        
        state_info = QLabel("""
🔍 测试方法：
1. 悬停测试：将鼠标悬停在▲▼按钮上，观察是否有反馈
2. 点击测试：点击▲▼按钮，测试数值增减功能
3. 边界测试：测试最小值和最大值的边界情况
4. 视觉测试：观察按钮位置是否合适，间距是否均匀

📐 间距说明：
• right: 2px - 按钮距离右边框2px
• top: 2px - 上按钮距离上边框2px  
• bottom: 2px - 下按钮距离下边框2px
• 左侧间距：由按钮宽度和控件宽度自动计算

✅ 预期效果：
• 按钮不会紧贴边框
• 视觉上更加协调美观
• 保持足够的点击区域
• 与整体界面风格一致
        """)
        state_layout.addWidget(state_info)
        
        # 创建一些测试用的SpinBox
        test_row = QHBoxLayout()
        
        for i, (label_text, suffix) in enumerate([
            ("测试1", " 个"),
            ("测试2", " %"),
            ("测试3", " 天"),
            ("测试4", "")
        ]):
            test_spinbox = QSpinBox()
            test_spinbox.setRange(1, 100)
            test_spinbox.setValue(10 + i * 10)
            if suffix:
                test_spinbox.setSuffix(suffix)
            test_spinbox.setMinimumWidth(80)
            test_spinbox.setMaximumWidth(100)
            
            test_col = QVBoxLayout()
            test_col.addWidget(QLabel(label_text))
            test_col.addWidget(test_spinbox)
            
            test_widget = QWidget()
            test_widget.setLayout(test_col)
            test_row.addWidget(test_widget)
        
        test_row.addStretch()
        state_layout.addLayout(test_row)
        
        layout.addWidget(state_group)
        
        # 测试说明
        test_info = QLabel("""
📐 SpinBox按钮间距优化总结：

✅ 间距设置：
• 上按钮（▲）：right: 2px, top: 2px
• 下按钮（▼）：right: 2px, bottom: 2px
• 按钮尺寸：20px宽 × 12px高
• 布局方式：上下垂直排列

🎯 优化效果：
• 视觉协调：按钮不再紧贴边框
• 间距均匀：上下左右都有合适的间距
• 点击友好：保持足够的点击区域
• 美观统一：与整体界面风格一致

🔍 适用控件：
• 高级筛选 → 最少重复、相同字符、相似度
• 编号文件筛选 → 数量、间隔
• 所有其他SpinBox控件

💡 技术实现：
• 使用CSS的right、top、bottom属性
• 精确控制按钮位置
• 保持系统默认箭头样式
• 兼容性和性能最佳
        """)
        test_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(test_info)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(WindowsTheme.get_complete_style())

def test_spinbox_margin():
    """测试SpinBox按钮间距"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行SpinBox间距测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("📐 SpinBox按钮间距测试启动")
    print("=" * 50)
    print("🎯 间距设置:")
    print("  • 上按钮（▲）：right: 2px, top: 2px")
    print("  • 下按钮（▼）：right: 2px, bottom: 2px")
    print("  • 按钮尺寸：20px宽 × 12px高")
    print("  • 布局方式：上下垂直排列")
    print()
    print("✅ 预期效果:")
    print("  • 按钮不再紧贴边框")
    print("  • 上下左右都有2px的间距")
    print("  • 视觉效果更加协调")
    print("  • 点击区域仍然足够大")
    print()
    print("🔍 测试重点:")
    print("  • 按钮位置是否合适")
    print("  • 间距是否均匀")
    print("  • 视觉效果是否美观")
    print("  • 功能是否正常工作")
    print("  • 与整体界面的协调性")
    print()
    print("💡 测试方法:")
    print("  • 观察高级筛选和编号文件筛选中的SpinBox")
    print("  • 测试不同尺寸SpinBox的按钮位置")
    print("  • 悬停和点击测试按钮交互")
    print("  • 对比间距调整前后的效果")
    print()
    
    # 创建测试窗口
    test_widget = SpinBoxMarginTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行SpinBox间距测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_spinbox_margin()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
