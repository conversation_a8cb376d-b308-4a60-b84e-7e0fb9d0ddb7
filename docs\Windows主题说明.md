# 文件筛选工具 - Windows主题说明

## 🎯 主题设计目标

实现Windows系统默认主题风格，采用渐变效果和现代化设计，提供更贴近Windows用户习惯的界面体验。

## 🎨 Windows主题特色

### 1. 颜色方案
参考Windows 11的设计语言，采用浅色主题配色：

```css
/* 主要颜色 */
background: #f3f3f3      /* 主背景 - 浅灰色 */
surface: #ffffff         /* 表面色 - 纯白色 */
primary: #0078d4         /* 主色调 - Windows蓝 */
text_primary: #323130    /* 主文本 - 深灰色 */
border: #d1d1d1          /* 边框色 - 中灰色 */
```

### 2. 渐变效果
大量使用CSS渐变，营造立体感和现代感：

#### 按钮渐变
```css
background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
    stop: 0 #0078d4,     /* 顶部 - 亮蓝 */
    stop: 1 #005a9e);    /* 底部 - 深蓝 */
```

#### 输入框渐变
```css
background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
    stop: 0 #ffffff,     /* 顶部 - 纯白 */
    stop: 1 #fafafa);    /* 底部 - 浅灰 */
```

#### 分组框渐变
```css
background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
    stop: 0 #ffffff,     /* 顶部 - 纯白 */
    stop: 1 #fafafa);    /* 底部 - 浅灰 */
```

### 3. 复选框✓符号
- **显示方式**：使用Unicode ✓ 符号
- **颜色**：白色，在蓝色背景上清晰可见
- **字体**：14px粗体，确保清晰度
- **对齐**：居中显示

### 4. 交互效果

#### 悬停效果
- **按钮**：渐变色变亮
- **输入框**：边框变蓝
- **复选框**：背景渐变到浅蓝色

#### 焦点效果
- **输入框**：2px蓝色边框
- **按钮**：渐变色加深
- **文本框**：蓝色边框高亮

#### 按下效果
- **按钮**：渐变反转，深色在上
- **SpinBox按钮**：蓝色渐变

## 📊 主题对比

| 特性 | VS主题 | Windows主题 |
|------|--------|-------------|
| 背景色 | 深色 (#1e1e1e) | 浅色 (#f3f3f3) |
| 主色调 | 蓝色 (#007acc) | Windows蓝 (#0078d4) |
| 文本色 | 浅色 (#d4d4d4) | 深色 (#323130) |
| 渐变效果 | 无 | 丰富的渐变 |
| 复选框 | 简单蓝色背景 | 渐变背景+✓符号 |
| 边框 | 细线条 | 2px粗边框 |
| 圆角 | 3px | 4-6px |
| 阴影 | 无 | 支持阴影效果 |

## 🛠️ 技术实现

### 1. 渐变语法
```css
/* 垂直渐变 */
qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
    stop: 0 color1, stop: 1 color2)

/* 水平渐变 */
qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
    stop: 0 color1, stop: 1 color2)
```

### 2. 复选框实现
```css
QCheckBox::indicator:checked {
    background: qlineargradient(...);
    border: 2px solid #0078d4;
}

QCheckBox::indicator:checked:after {
    content: "✓";
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
}
```

### 3. 主题切换
```python
def apply_theme(self):
    """应用主题"""
    if self.current_theme == "windows":
        self.setStyleSheet(WindowsTheme.get_complete_style())
    else:
        self.setStyleSheet(VSTheme.get_complete_style())

def switch_theme(self):
    """切换主题"""
    self.current_theme = "vs" if self.current_theme == "windows" else "windows"
    self.apply_theme()
```

## 🎯 设计原则

### 1. Windows设计语言
- **Fluent Design**：现代化的设计语言
- **一致性**：与Windows系统界面保持一致
- **可访问性**：高对比度，易于阅读

### 2. 用户体验
- **熟悉感**：Windows用户习惯的界面风格
- **直观性**：清晰的视觉层次和交互反馈
- **舒适性**：浅色主题减少眼部疲劳

### 3. 现代化
- **渐变效果**：立体感和层次感
- **圆角设计**：柔和的视觉效果
- **微交互**：丰富的悬停和焦点效果

## 🚀 使用方式

### 主题切换
1. **菜单切换**：工具 → 切换主题 (Ctrl+T)
2. **默认主题**：Windows主题
3. **备选主题**：Visual Studio深色主题

### 最佳实践
- **日间使用**：Windows浅色主题
- **夜间使用**：VS深色主题
- **长时间工作**：根据个人喜好选择

## 🔧 自定义选项

### 颜色调整
可以通过修改 `WindowsTheme.COLORS` 来自定义颜色：

```python
COLORS = {
    'primary': '#0078d4',        # 主色调
    'primary_hover': '#106ebe',  # 悬停色
    'background': '#f3f3f3',     # 背景色
    # ... 其他颜色
}
```

### 渐变调整
可以修改渐变的起止颜色和方向：

```css
/* 自定义渐变 */
qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
    stop: 0 #your_color1, 
    stop: 1 #your_color2)
```

## 📝 后续计划

### 短期目标
- [ ] 添加更多渐变效果
- [ ] 优化复选框显示
- [ ] 完善阴影效果

### 长期目标
- [ ] 支持Windows 11 Mica效果
- [ ] 自定义主题编辑器
- [ ] 主题配置保存

## 🎯 总结

Windows主题为文件筛选工具带来了：

1. **现代化外观**：渐变效果和圆角设计
2. **Windows风格**：符合Windows用户习惯
3. **良好的可读性**：高对比度的浅色主题
4. **丰富的交互**：悬停、焦点、按下效果
5. **主题切换**：支持与VS主题之间切换

现在用户可以享受到更加美观和现代化的界面体验！

---

**版本**: v1.2 - Windows主题版  
**设计时间**: 2024年  
**开发者**: Andy_127【浅醉丶墨语】  
**联系方式**: yangjun_127

🎨 **主题特色**：Windows风格渐变主题，复选框✓符号，现代化设计！
