#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标签页主界面
实现标签页界面 + 双目录对比分屏布局的混合方案
"""

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
        QLabel, QGroupBox, QSplitter, QPushButton, QFrame
    )
    from PySide6.QtCore import Qt, Signal
    from PySide6.QtGui import QFont, QIcon
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

from .normal_search_tab import NormalSearchTab
from .dual_directory_tab import DualDirectoryTab
from .duplicate_cleanup_tab import DuplicateCleanupTab
from .batch_operations_tab import BatchOperationsTab


class MainTabbedInterface(QWidget):
    """标签页主界面"""
    
    # 信号定义
    status_message = Signal(str)
    progress_update = Signal(int, int, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建标题栏
        title_frame = self.create_title_frame()
        layout.addWidget(title_frame)
        
        # 创建主标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setTabsClosable(False)
        
        # 创建各个标签页
        self.create_tabs()
        
        layout.addWidget(self.tab_widget)
        
        # 创建状态栏
        status_frame = self.create_status_frame()
        layout.addWidget(status_frame)
    
    def create_title_frame(self):
        """创建标题栏"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.StyledPanel)
        frame.setMaximumHeight(60)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # 应用图标和标题
        title_label = QLabel("🗂️ 智能文件管理工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50;")
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # 版本信息
        version_label = QLabel("v2.0")
        version_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        layout.addWidget(version_label)
        
        return frame
    
    def create_status_frame(self):
        """创建状态栏"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.StyledPanel)
        frame.setMaximumHeight(30)
        
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(10, 5, 10, 5)
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #34495e; font-size: 11px;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 当前标签页指示
        self.current_tab_label = QLabel("普通搜索")
        self.current_tab_label.setStyleSheet("color: #3498db; font-size: 11px; font-weight: bold;")
        layout.addWidget(self.current_tab_label)
        
        return frame
    
    def create_tabs(self):
        """创建所有标签页"""
        # 1. 普通搜索标签页
        self.normal_search_tab = NormalSearchTab()
        self.tab_widget.addTab(self.normal_search_tab, "🔍 普通搜索")
        
        # 2. 双目录对比标签页
        self.dual_directory_tab = DualDirectoryTab()
        self.tab_widget.addTab(self.dual_directory_tab, "🔄 双目录对比")
        
        # 3. 重复文件清理标签页
        self.duplicate_cleanup_tab = DuplicateCleanupTab()
        self.tab_widget.addTab(self.duplicate_cleanup_tab, "🗂️ 重复清理")
        
        # 4. 批量操作标签页
        self.batch_operations_tab = BatchOperationsTab()
        self.tab_widget.addTab(self.batch_operations_tab, "📊 批量操作")
        
        # 设置默认标签页
        self.tab_widget.setCurrentIndex(0)
    
    def connect_signals(self):
        """连接信号"""
        # 标签页切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        # 连接各标签页的状态信号
        self.normal_search_tab.status_message.connect(self.update_status)
        self.dual_directory_tab.status_message.connect(self.update_status)
        self.duplicate_cleanup_tab.status_message.connect(self.update_status)
        self.batch_operations_tab.status_message.connect(self.update_status)
        
        # 连接进度信号
        self.normal_search_tab.progress_update.connect(self.progress_update.emit)
        self.dual_directory_tab.progress_update.connect(self.progress_update.emit)
        self.duplicate_cleanup_tab.progress_update.connect(self.progress_update.emit)
        self.batch_operations_tab.progress_update.connect(self.progress_update.emit)
    
    def on_tab_changed(self, index):
        """标签页切换处理"""
        tab_names = ["普通搜索", "双目录对比", "重复清理", "批量操作"]
        if 0 <= index < len(tab_names):
            self.current_tab_label.setText(tab_names[index])
            self.update_status(f"切换到{tab_names[index]}模式")
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.setText(message)
        self.status_message.emit(message)
    
    def get_current_tab(self):
        """获取当前标签页"""
        current_index = self.tab_widget.currentIndex()
        return self.tab_widget.widget(current_index)
    
    def switch_to_tab(self, tab_name):
        """切换到指定标签页"""
        tab_map = {
            "normal": 0,
            "dual_directory": 1,
            "duplicate": 2,
            "batch": 3
        }
        
        if tab_name in tab_map:
            self.tab_widget.setCurrentIndex(tab_map[tab_name])
    
    def clear_all_tabs(self):
        """清空所有标签页的设置"""
        self.normal_search_tab.clear_settings()
        self.dual_directory_tab.clear_settings()
        self.duplicate_cleanup_tab.clear_settings()
        self.batch_operations_tab.clear_settings()
        self.update_status("已清空所有设置")
    
    def get_tab_settings(self, tab_name):
        """获取指定标签页的设置"""
        tab_map = {
            "normal": self.normal_search_tab,
            "dual_directory": self.dual_directory_tab,
            "duplicate": self.duplicate_cleanup_tab,
            "batch": self.batch_operations_tab
        }
        
        if tab_name in tab_map:
            return tab_map[tab_name].get_settings()
        return None
    
    def set_tab_enabled(self, tab_name, enabled):
        """设置标签页的启用状态"""
        tab_map = {
            "normal": 0,
            "dual_directory": 1,
            "duplicate": 2,
            "batch": 3
        }
        
        if tab_name in tab_map:
            self.tab_widget.setTabEnabled(tab_map[tab_name], enabled)
    
    def add_custom_tab(self, widget, title, icon=None):
        """添加自定义标签页"""
        if icon:
            self.tab_widget.addTab(widget, icon, title)
        else:
            self.tab_widget.addTab(widget, title)
    
    def remove_tab(self, index):
        """移除标签页"""
        if 0 <= index < self.tab_widget.count():
            self.tab_widget.removeTab(index)
    
    def set_tab_style(self, style_sheet):
        """设置标签页样式"""
        self.tab_widget.setStyleSheet(style_sheet)
    
    def get_all_tab_data(self):
        """获取所有标签页的数据"""
        return {
            "normal_search": self.normal_search_tab.get_settings(),
            "dual_directory": self.dual_directory_tab.get_settings(),
            "duplicate_cleanup": self.duplicate_cleanup_tab.get_settings(),
            "batch_operations": self.batch_operations_tab.get_settings(),
            "current_tab": self.tab_widget.currentIndex()
        }
    
    def load_tab_data(self, data):
        """加载标签页数据"""
        if "normal_search" in data:
            self.normal_search_tab.load_settings(data["normal_search"])
        if "dual_directory" in data:
            self.dual_directory_tab.load_settings(data["dual_directory"])
        if "duplicate_cleanup" in data:
            self.duplicate_cleanup_tab.load_settings(data["duplicate_cleanup"])
        if "batch_operations" in data:
            self.batch_operations_tab.load_settings(data["batch_operations"])
        if "current_tab" in data:
            self.tab_widget.setCurrentIndex(data["current_tab"])
    
    def export_settings(self, file_path):
        """导出所有设置到文件"""
        import json
        try:
            data = self.get_all_tab_data()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            self.update_status(f"设置已导出到: {file_path}")
            return True
        except Exception as e:
            self.update_status(f"导出设置失败: {e}")
            return False
    
    def import_settings(self, file_path):
        """从文件导入设置"""
        import json
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.load_tab_data(data)
            self.update_status(f"设置已从文件导入: {file_path}")
            return True
        except Exception as e:
            self.update_status(f"导入设置失败: {e}")
            return False
