#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双目录文件对比功能测试
测试双目录对比和智能按键功能
"""

import sys
import tempfile
import time
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QTextEdit, QLineEdit
    )
    from file_filter_tool.core.dual_directory_comparator import DualDirectoryComparator
    from file_filter_tool.file_filter import FileFilter
    from file_filter_tool.ui.components import AdvancedFilterWidget, OperationButtonsWidget
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class DualDirectoryTestWidget(QWidget):
    """双目录对比测试控件"""
    
    def __init__(self):
        super().__init__()
        self.temp_dir = None
        self.test_files = []
        self.dual_comparator = DualDirectoryComparator()
        self.setup_ui()
        self.create_test_structure()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("双目录文件对比功能测试")
        self.setMinimumSize(1200, 900)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🔄 双目录文件对比功能测试

本测试验证以下功能：

✅ 双目录对比核心功能：
• 完全匹配：文件名完全相同
• 忽略扩展名：只比较文件名主体
• 编号匹配：提取文件名中的编号进行匹配
• 时间比较：修改时间、创建时间、访问时间
• 递归搜索：包含子目录下的文件

✅ 智能按键功能：
• 普通模式：搜索文件、复制选中、移动选中、删除选中
• 双目录模式：开始对比、保留新文件、备份旧文件、删除旧文件
• 模式指示：显示当前操作模式

✅ 文件处理策略：
• 仅显示：显示所有匹配的文件对
• 保留最新：删除较旧的文件，保留最新的
• 备份旧文件：将较旧的文件移动到备份目录

🎯 测试场景：
• 创建两个子目录，包含各种类型的文件
• 模拟真实的文件对比场景
• 测试不同匹配模式和时间策略
• 验证智能按键的模式切换
        """)
        layout.addWidget(info_label)
        
        # 测试控制区域
        control_group = QGroupBox("测试控制")
        control_layout = QHBoxLayout(control_group)
        
        # 创建测试结构按钮
        create_btn = QPushButton("创建测试结构")
        create_btn.clicked.connect(self.create_test_structure)
        control_layout.addWidget(create_btn)
        
        # 基础对比测试按钮
        basic_btn = QPushButton("基础对比测试")
        basic_btn.clicked.connect(self.test_basic_comparison)
        control_layout.addWidget(basic_btn)
        
        # 匹配模式测试按钮
        match_btn = QPushButton("匹配模式测试")
        match_btn.clicked.connect(self.test_match_modes)
        control_layout.addWidget(match_btn)
        
        # 智能按键测试按钮
        button_btn = QPushButton("智能按键测试")
        button_btn.clicked.connect(self.test_smart_buttons)
        control_layout.addWidget(button_btn)
        
        # 清理按钮
        cleanup_btn = QPushButton("清理测试文件")
        cleanup_btn.clicked.connect(self.cleanup_test_files)
        control_layout.addWidget(cleanup_btn)
        
        layout.addWidget(control_group)
        
        # UI组件测试区域
        ui_group = QGroupBox("UI组件测试")
        ui_layout = QVBoxLayout(ui_group)
        
        # 高级筛选组件
        filter_label = QLabel("高级筛选组件（包含双目录对比）：")
        filter_label.setStyleSheet("font-weight: bold; color: #0078d4;")
        ui_layout.addWidget(filter_label)
        
        self.advanced_filter = AdvancedFilterWidget()
        ui_layout.addWidget(self.advanced_filter)
        
        # 操作按钮组件
        button_label = QLabel("智能操作按钮组件：")
        button_label.setStyleSheet("font-weight: bold; color: #0078d4; margin-top: 10px;")
        ui_layout.addWidget(button_label)
        
        self.operation_buttons = OperationButtonsWidget()
        ui_layout.addWidget(self.operation_buttons)
        
        # 连接信号
        if hasattr(self.advanced_filter, 'dual_dir_check'):
            self.advanced_filter.dual_dir_check.stateChanged.connect(self.on_mode_changed)
        
        layout.addWidget(ui_group)
        
        # 测试输入区域
        input_group = QGroupBox("手动测试输入")
        input_layout = QVBoxLayout(input_group)
        
        # 子目录输入
        dir_row = QHBoxLayout()
        dir_row.addWidget(QLabel("子目录1:"))
        self.subdir1_input = QLineEdit("dir1")
        dir_row.addWidget(self.subdir1_input)
        
        dir_row.addWidget(QLabel("子目录2:"))
        self.subdir2_input = QLineEdit("dir2")
        dir_row.addWidget(self.subdir2_input)
        
        manual_test_btn = QPushButton("手动对比测试")
        manual_test_btn.clicked.connect(self.manual_comparison_test)
        dir_row.addWidget(manual_test_btn)
        
        input_layout.addLayout(dir_row)
        layout.addWidget(input_group)
        
        # 结果显示区域
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMinimumHeight(300)
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
    
    def create_test_structure(self):
        """创建测试目录结构"""
        try:
            # 清理旧的测试文件
            self.cleanup_test_files()
            
            # 创建临时目录
            self.temp_dir = Path(tempfile.mkdtemp(prefix="dual_dir_test_"))
            self.log(f"创建测试目录: {self.temp_dir}")
            
            # 创建两个子目录
            dir1 = self.temp_dir / "dir1"
            dir2 = self.temp_dir / "dir2"
            dir1.mkdir()
            dir2.mkdir()
            
            # 创建子目录
            (dir1 / "subdir").mkdir()
            (dir2 / "subdir").mkdir()
            
            # 在dir1中创建文件
            test_files_dir1 = [
                ("document.txt", "这是一个文档文件", time.time() - 3600),  # 1小时前
                ("image.jpg", b"fake_jpeg_data", time.time() - 7200),      # 2小时前
                ("report_001.doc", "报告文档1", time.time() - 1800),       # 30分钟前
                ("data.csv", "数据文件", time.time() - 5400),              # 1.5小时前
                ("subdir/nested.txt", "嵌套文件", time.time() - 900),      # 15分钟前
            ]
            
            # 在dir2中创建文件（部分相同，部分不同时间）
            test_files_dir2 = [
                ("document.txt", "这是一个文档文件", time.time()),          # 现在（更新）
                ("image.png", b"fake_png_data", time.time() - 3600),       # 不同扩展名
                ("report_001.doc", "报告文档1", time.time() - 3600),       # 1小时前（更旧）
                ("video.mp4", b"fake_video_data", time.time()),            # 独有文件
                ("subdir/nested.txt", "嵌套文件", time.time() - 1800),     # 30分钟前（更旧）
                ("report_002.doc", "报告文档2", time.time()),              # 新编号文件
            ]
            
            # 创建dir1中的文件
            for filename, content, mtime in test_files_dir1:
                file_path = dir1 / filename
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                if isinstance(content, str):
                    file_path.write_text(content, encoding='utf-8')
                else:
                    file_path.write_bytes(content)
                
                # 设置修改时间
                import os
                os.utime(file_path, (mtime, mtime))
            
            # 创建dir2中的文件
            for filename, content, mtime in test_files_dir2:
                file_path = dir2 / filename
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                if isinstance(content, str):
                    file_path.write_text(content, encoding='utf-8')
                else:
                    file_path.write_bytes(content)
                
                # 设置修改时间
                import os
                os.utime(file_path, (mtime, mtime))
            
            self.log("测试目录结构创建完成！")
            self.log(f"dir1: {len(test_files_dir1)} 个文件")
            self.log(f"dir2: {len(test_files_dir2)} 个文件")
            self.log("包含完全匹配、扩展名不同、编号匹配等场景")
            
        except Exception as e:
            self.log(f"创建测试结构失败: {e}")
    
    def test_basic_comparison(self):
        """测试基础对比功能"""
        if not self.temp_dir:
            self.log("请先创建测试结构")
            return
        
        try:
            self.log("\n=== 基础双目录对比测试 ===")
            
            # 执行对比
            result = self.dual_comparator.compare_directories(
                root_path=self.temp_dir,
                subdir1="dir1",
                subdir2="dir2",
                match_mode="exact",
                time_mode="modified"
            )
            
            # 显示结果
            stats = result['statistics']
            self.log(f"对比完成:")
            self.log(f"  dir1文件数: {stats['total_files1']}")
            self.log(f"  dir2文件数: {stats['total_files2']}")
            self.log(f"  匹配文件对: {stats['matched_pairs']}")
            self.log(f"  dir1独有: {stats['unique_files1']}")
            self.log(f"  dir2独有: {stats['unique_files2']}")
            self.log(f"  可节省空间: {stats['space_saving']}")
            
            # 显示匹配的文件对
            self.log("\n匹配的文件对:")
            for i, result_item in enumerate(result['comparison_results'], 1):
                file1 = result_item['file1']
                file2 = result_item['file2']
                newer = result_item['newer_file']
                time_diff = result_item['time_diff']
                
                self.log(f"  {i}. {file1.name} <-> {file2.name}")
                self.log(f"     较新文件: {newer.name} (时间差: {time_diff:.0f}秒)")
            
        except Exception as e:
            self.log(f"基础对比测试失败: {e}")
    
    def test_match_modes(self):
        """测试不同匹配模式"""
        if not self.temp_dir:
            self.log("请先创建测试结构")
            return
        
        try:
            self.log("\n=== 匹配模式测试 ===")
            
            modes = [
                ("exact", "完全匹配"),
                ("ignore_ext", "忽略扩展名"),
                ("number", "编号匹配")
            ]
            
            for mode, mode_name in modes:
                self.log(f"\n{mode_name}模式:")
                
                result = self.dual_comparator.compare_directories(
                    root_path=self.temp_dir,
                    subdir1="dir1",
                    subdir2="dir2",
                    match_mode=mode,
                    time_mode="modified"
                )
                
                matched_pairs = len(result['file_pairs'])
                self.log(f"  找到 {matched_pairs} 对匹配文件")
                
                for pair in result['file_pairs'][:3]:  # 只显示前3对
                    self.log(f"    {pair[0].name} <-> {pair[1].name}")
                
                if matched_pairs > 3:
                    self.log(f"    ... 还有 {matched_pairs - 3} 对")
            
        except Exception as e:
            self.log(f"匹配模式测试失败: {e}")
    
    def test_smart_buttons(self):
        """测试智能按键功能"""
        self.log("\n=== 智能按键测试 ===")
        
        # 测试模式切换
        self.log("测试按键模式切换:")
        
        # 普通模式
        self.operation_buttons.set_mode("normal")
        self.log(f"  普通模式: {self.operation_buttons.search_btn.text()}")
        
        # 双目录模式
        self.operation_buttons.set_mode("dual_directory")
        self.log(f"  双目录模式: {self.operation_buttons.search_btn.text()}")
        
        # 重复文件模式
        self.operation_buttons.set_mode("duplicate")
        self.log(f"  重复文件模式: {self.operation_buttons.search_btn.text()}")
        
        # 恢复普通模式
        self.operation_buttons.set_mode("normal")
        
        self.log("智能按键模式切换测试完成")
    
    def manual_comparison_test(self):
        """手动对比测试"""
        if not self.temp_dir:
            self.log("请先创建测试结构")
            return
        
        subdir1 = self.subdir1_input.text().strip()
        subdir2 = self.subdir2_input.text().strip()
        
        if not subdir1 or not subdir2:
            self.log("请输入两个子目录名称")
            return
        
        try:
            self.log(f"\n=== 手动对比测试: {subdir1} vs {subdir2} ===")
            
            # 获取高级筛选设置
            dual_settings = self.advanced_filter.get_dual_directory_settings()
            
            if dual_settings:
                match_mode = dual_settings['match_mode']
                time_mode = dual_settings['time_mode']
                self.log(f"使用设置: 匹配模式={match_mode}, 时间模式={time_mode}")
            else:
                match_mode = "exact"
                time_mode = "modified"
                self.log("使用默认设置")
            
            # 执行对比
            result = self.dual_comparator.compare_directories(
                root_path=self.temp_dir,
                subdir1=subdir1,
                subdir2=subdir2,
                match_mode=match_mode,
                time_mode=time_mode
            )
            
            # 显示结果
            stats = result['statistics']
            self.log(f"对比结果: 找到 {stats['matched_pairs']} 对匹配文件")
            
        except Exception as e:
            self.log(f"手动对比测试失败: {e}")
    
    def on_mode_changed(self, checked):
        """模式变化处理"""
        if checked:
            self.operation_buttons.set_mode("dual_directory")
            self.log("UI模式切换: 双目录对比模式")
        else:
            self.operation_buttons.set_mode("normal")
            self.log("UI模式切换: 普通模式")
    
    def log(self, message: str):
        """记录日志"""
        self.result_text.append(message)
        self.result_text.ensureCursorVisible()
        QApplication.processEvents()  # 更新UI
    
    def cleanup_test_files(self):
        """清理测试文件"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                self.log(f"清理测试目录: {self.temp_dir}")
                self.temp_dir = None
            except Exception as e:
                self.log(f"清理失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.cleanup_test_files()
        event.accept()

def test_dual_directory_comparison():
    """测试双目录对比功能"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行双目录对比测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🔄 双目录文件对比功能测试启动")
    print("=" * 60)
    print("✅ 测试功能:")
    print("  • 双目录对比核心逻辑")
    print("  • 多种匹配模式（完全、忽略扩展名、编号）")
    print("  • 时间比较策略")
    print("  • 智能按键模式切换")
    print("  • UI组件集成")
    print()
    print("🎯 测试场景:")
    print("  • 创建真实的文件对比场景")
    print("  • 测试各种匹配模式")
    print("  • 验证智能按键功能")
    print("  • UI组件交互测试")
    print()
    
    # 创建测试窗口
    test_widget = DualDirectoryTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行双目录对比测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_dual_directory_comparison()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
