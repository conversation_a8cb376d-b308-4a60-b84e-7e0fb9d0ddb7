#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpinBox箭头美化测试
测试上下布局的美化箭头效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QSpinBox, QComboBox, QDateEdit
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.themes import WindowsTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class ArrowTestWidget(QWidget):
    """箭头美化测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("SpinBox箭头美化测试 - 上下布局 + 渐变效果")
        self.setMinimumSize(800, 700)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🎨 SpinBox箭头美化测试

本测试验证美化后的箭头效果：

✨ 美化特点：
• 上下布局：增加/减少按钮垂直排列
• 按钮尺寸：24px宽 × 14px高（增大点击区域）
• 渐变背景：白色到灰色的渐变效果
• 悬停效果：蓝色渐变背景 + 白色箭头
• 按下效果：深蓝色渐变背景 + 白色箭头
• 箭头符号：6px宽 × 8px高的清晰三角形

🎯 测试重点：
• SpinBox的▲▼按钮是否为上下布局
• 按钮背景渐变效果是否美观
• 悬停时的蓝色渐变效果
• 按下时的深蓝色渐变效果
• 箭头符号是否清晰可见
• ComboBox和DateEdit的下拉箭头效果

🔍 对比测试：
• 宽度加大的SpinBox（90-110px）
• 不同类型的数值输入
• 各种下拉选择控件
        """)
        layout.addWidget(info_label)
        
        # 测试组1：加宽的SpinBox测试
        wide_group = QGroupBox("加宽SpinBox测试 (90-110px)")
        wide_layout = QVBoxLayout(wide_group)
        
        wide_tests = [
            ("数量选择", 1, 100, 5, " 个"),
            ("间隔天数", 1, 365, 7, " 天"),
            ("保留文件", 1, 999, 10, " 个"),
            ("时间间隔", 1, 30, 3, " 天"),
            ("百分比", 0, 100, 50, " %"),
        ]
        
        for label_text, min_val, max_val, default_val, suffix in wide_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            if suffix:
                spinbox.setSuffix(suffix)
            spinbox.setMinimumWidth(90)
            spinbox.setMaximumWidth(110)
            row_layout.addWidget(spinbox)
            
            # 添加说明
            desc_label = QLabel("← 测试上下布局▲▼箭头 + 渐变背景")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            wide_layout.addLayout(row_layout)
        
        layout.addWidget(wide_group)
        
        # 测试组2：ComboBox下拉箭头测试
        combo_group = QGroupBox("ComboBox下拉箭头测试")
        combo_layout = QVBoxLayout(combo_group)
        
        combo_tests = [
            ("策略选择", ["保留最新", "保留最旧", "保留最新N个", "保留最旧N个"]),
            ("文件类型", [".txt", ".py", ".jpg", ".pdf", ".doc"]),
            ("排序方式", ["按名称", "按大小", "按时间", "按类型"]),
            ("时间单位", ["天", "周", "月", "年"]),
        ]
        
        for label_text, items in combo_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            combo = QComboBox()
            combo.addItems(items)
            combo.setMinimumWidth(120)
            combo.setMaximumWidth(150)
            row_layout.addWidget(combo)
            
            # 添加说明
            desc_label = QLabel("← 测试美化下拉▼箭头 + 渐变按钮")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            combo_layout.addLayout(row_layout)
        
        layout.addWidget(combo_group)
        
        # 测试组3：DateEdit日历箭头测试
        date_group = QGroupBox("DateEdit日历箭头测试")
        date_layout = QVBoxLayout(date_group)
        
        date_tests = [
            ("开始日期", -30),
            ("结束日期", 0),
            ("创建日期", -7),
            ("修改日期", -1),
        ]
        
        for label_text, days_offset in date_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(80)
            row_layout.addWidget(label)
            
            date_edit = QDateEdit()
            date_edit.setCalendarPopup(True)
            date_edit.setDate(QDate.currentDate().addDays(days_offset))
            date_edit.setDisplayFormat("yyyy年MM月dd日")
            date_edit.setMinimumWidth(120)
            date_edit.setMaximumWidth(150)
            row_layout.addWidget(date_edit)
            
            # 添加说明
            desc_label = QLabel("← 测试日历▼箭头 + 渐变按钮")
            desc_label.setStyleSheet("color: #666666; font-style: italic;")
            row_layout.addWidget(desc_label)
            
            row_layout.addStretch()
            date_layout.addLayout(row_layout)
        
        layout.addWidget(date_group)
        
        # 测试说明
        test_info = QLabel("""
🎨 美化效果说明：

✅ SpinBox按钮美化：
• 上下布局：▲增加按钮在上，▼减少按钮在下
• 按钮尺寸：24px × 14px，更大的点击区域
• 渐变背景：白色到浅灰色的优雅渐变
• 悬停效果：浅蓝色到蓝色的渐变 + 白色箭头
• 按下效果：蓝色到深蓝色的渐变 + 白色箭头

✅ ComboBox/DateEdit按钮美化：
• 按钮尺寸：24px宽，与SpinBox保持一致
• 渐变背景：与SpinBox相同的渐变效果
• 箭头符号：6px × 8px的清晰下拉箭头
• 交互反馈：悬停和按下时的颜色变化

🔍 测试方法：
1. 悬停在各种按钮上，观察渐变背景变化
2. 点击按钮，观察按下时的深色渐变效果
3. 注意箭头符号的清晰度和颜色变化
4. 测试SpinBox的上下布局是否正确
        """)
        test_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(test_info)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(WindowsTheme.get_complete_style())

def test_spinbox_arrows():
    """测试SpinBox箭头美化"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行箭头美化测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🎨 SpinBox箭头美化测试启动")
    print("=" * 50)
    print("✨ 美化特点:")
    print("  • SpinBox按钮：上下布局（▲在上，▼在下）")
    print("  • 按钮尺寸：24px宽 × 14px高")
    print("  • 渐变背景：白色到灰色的优雅渐变")
    print("  • 悬停效果：蓝色渐变背景 + 白色箭头")
    print("  • 按下效果：深蓝色渐变背景 + 白色箭头")
    print("  • 箭头符号：6px × 8px的清晰三角形")
    print()
    print("🎯 测试重点:")
    print("  • SpinBox数量和间隔控件宽度：90-110px")
    print("  • 上下布局的按钮排列")
    print("  • 渐变背景的美观效果")
    print("  • 悬停和按下时的颜色变化")
    print("  • ComboBox和DateEdit的下拉箭头美化")
    print()
    print("🔍 验证方法:")
    print("  • 悬停在按钮上观察渐变背景变化")
    print("  • 点击按钮测试深色渐变效果")
    print("  • 检查箭头符号的清晰度")
    print("  • 验证SpinBox的上下布局")
    print()
    
    # 创建测试窗口
    test_widget = ArrowTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行箭头美化测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_spinbox_arrows()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
