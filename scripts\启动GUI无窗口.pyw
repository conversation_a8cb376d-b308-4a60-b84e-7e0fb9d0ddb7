#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 无窗口启动器
使用.pyw扩展名隐藏命令行窗口
"""

import sys
import os
from pathlib import Path

def check_pyside6():
    """检查PySide6是否可用"""
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt, QThread, Signal
        from PySide6.QtGui import QIcon
        return True
    except ImportError:
        return False

def check_tkinter():
    """检查tkinter是否可用"""
    try:
        import tkinter as tk
        # 尝试创建测试窗口
        root = tk.Tk()
        root.withdraw()
        root.destroy()
        return True
    except ImportError:
        return False
    except Exception:
        return False

def show_error_dialog(message):
    """显示错误对话框"""
    try:
        # 尝试使用tkinter显示错误
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        messagebox.showerror("启动错误", message)
        root.destroy()
    except:
        # 如果tkinter也不可用，写入错误日志
        try:
            with open("error.log", "w", encoding="utf-8") as f:
                f.write(f"启动错误: {message}\n")
                f.write("请检查Python环境和依赖包安装情况\n")
        except:
            pass

def main():
    """主函数"""
    # 检查PySide6
    if check_pyside6():
        try:
            import gui_pyside6
            gui_pyside6.main()
            return
        except Exception as e:
            show_error_dialog(f"PySide6界面启动失败: {e}")
    
    # 检查tkinter
    if check_tkinter():
        try:
            import gui
            gui.main()
            return
        except Exception as e:
            show_error_dialog(f"tkinter界面启动失败: {e}")
    
    # 都不可用，显示错误信息
    error_msg = """
无法启动图形界面！

可能的原因：
1. 缺少PySide6：请运行 pip install PySide6
2. tkinter未正确安装
3. Python环境配置问题

建议解决方案：
1. 安装PySide6：pip install PySide6
2. 检查Python版本（需要3.6+）
3. 重新安装Python并确保包含tkinter

如需帮助，请查看error.log文件或联系开发者。
    """
    show_error_dialog(error_msg.strip())

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        show_error_dialog(f"程序异常退出: {e}")
