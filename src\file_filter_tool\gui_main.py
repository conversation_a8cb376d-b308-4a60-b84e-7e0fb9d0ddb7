"""
主窗口模块 - 重构版
使用拆分的UI组件和主题
"""

import sys
import os
from pathlib import Path

try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog,
        QMessageBox, QProgressBar, QStatusBar, QGroupBox, QSplitter,
        QLabel
    )
    from PySide6.QtCore import Qt, QTimer
    from PySide6.QtGui import QIcon, QAction, QKeySequence
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

from .file_filter import FileFilter
from .config import ConfigManager, FilterConfig
from .ui import (
    WindowsTheme, SearchPathWidget, FilterConditionsWidget,
    AdvancedFilterWidget, OperationButtonsWidget, LogWidget, CustomCheckBox,
    NumberedFileFilterWidget
)
from .core import FileSearchThread, FileOperationThread, NumberedFileFilter

class FileFilterMainWindow(QMainWindow):
    """主窗口类 - 重构版"""
    
    def __init__(self):
        super().__init__()
        self.file_filter = None
        self.config_manager = ConfigManager()
        self.found_files = []
        self.duplicate_groups = {}
        self.similar_groups = {}
        self.search_thread = None
        self.operation_thread = None
        self.current_theme = "windows"  # 默认使用Windows主题

        self.setWindowTitle("文件筛选过滤工具 v1.2")
        # 设置更合理的窗口大小
        self.setMinimumSize(1000, 700)  # 减小最小尺寸
        self.resize(1300, 850)  # 稍微减小默认尺寸

        # 设置窗口大小策略
        from PySide6.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)

        # 设置窗口图标
        self.setup_icon()
        
        # 应用主题样式
        self.apply_theme()

        # 创建界面
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()

        # 设置默认值
        self.search_path_widget.set_path(".")
        self.search_path_widget.set_recursive(True)
        
        # 初始化日志
        self.log_widget.log_message("程序启动完成，就绪")

    def setup_icon(self):
        """设置应用程序图标"""
        try:
            project_root = Path(__file__).parent.parent.parent
            
            # 尝试加载ICO图标
            icon_path = project_root / "assets" / "res" / "logo.ico"
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                # Windows任务栏图标修复
                if sys.platform == "win32":
                    try:
                        import ctypes
                        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("FileFilterTool.1.2")
                    except:
                        pass
                return
            
            # 备选PNG图标
            icon_path = project_root / "assets" / "res" / "logo.png"
            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                if sys.platform == "win32":
                    try:
                        import ctypes
                        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID("FileFilterTool.1.2")
                    except:
                        pass
        except Exception as e:
            print(f"设置图标时出错: {e}")

    def apply_theme(self):
        """应用主题"""
        # 只使用Windows主题
        style = WindowsTheme.get_complete_style()
        self.setStyleSheet(style)
        # 设置窗口属性以确保主题生效
        self.setWindowTitle("文件筛选过滤工具 v1.2 - Windows主题")
        # 更新自定义复选框样式
        self.update_checkbox_styles("windows")

        # 强制刷新界面
        self.update()
        self.repaint()

    def update_checkbox_styles(self, theme):
        """更新所有自定义复选框的样式"""
        # 查找所有CustomCheckBox控件
        checkboxes = self.findChildren(CustomCheckBox)
        for checkbox in checkboxes:
            # 只使用Windows样式
            checkbox.apply_windows_style()



    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # 创建上下分割器
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：控制面板
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(6)

        # 搜索路径组件
        path_group = QGroupBox("搜索路径")
        path_layout = QVBoxLayout(path_group)
        path_layout.setContentsMargins(8, 8, 8, 8)
        self.search_path_widget = SearchPathWidget()
        path_layout.addWidget(self.search_path_widget)
        control_layout.addWidget(path_group)

        # 创建水平分割器放置筛选条件
        filter_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 筛选条件组件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QVBoxLayout(filter_group)
        filter_layout.setContentsMargins(8, 8, 8, 8)
        self.filter_conditions_widget = FilterConditionsWidget()
        filter_layout.addWidget(self.filter_conditions_widget)
        filter_splitter.addWidget(filter_group)

        # 高级筛选组件
        advanced_group = QGroupBox("高级筛选")
        advanced_layout = QVBoxLayout(advanced_group)
        advanced_layout.setContentsMargins(8, 8, 8, 8)
        self.advanced_filter_widget = AdvancedFilterWidget()
        advanced_layout.addWidget(self.advanced_filter_widget)

        # 添加编号文件筛选组件
        self.numbered_file_widget = NumberedFileFilterWidget()
        advanced_layout.addWidget(self.numbered_file_widget)

        filter_splitter.addWidget(advanced_group)

        # 设置筛选器比例
        filter_splitter.setSizes([300, 200])
        control_layout.addWidget(filter_splitter)

        # 操作按钮组件
        self.operation_buttons_widget = OperationButtonsWidget()
        control_layout.addWidget(self.operation_buttons_widget)

        main_splitter.addWidget(control_widget)

        # 下半部分：结果和日志
        result_widget = QWidget()
        result_layout = QVBoxLayout(result_widget)
        result_layout.setContentsMargins(0, 0, 0, 0)
        result_layout.setSpacing(6)

        # 结果表格
        self.setup_result_table()
        result_layout.addWidget(self.result_table)

        # 日志组件
        log_label = QLabel("操作日志:")
        log_label.setMaximumHeight(20)
        result_layout.addWidget(log_label)
        self.log_widget = LogWidget()
        result_layout.addWidget(self.log_widget)

        main_splitter.addWidget(result_widget)

        # 设置主分割器比例 (控制面板:结果区域 = 1:2)
        main_splitter.setSizes([300, 600])

        layout.addWidget(main_splitter)

        # 连接信号
        self.connect_signals()

    def setup_result_table(self):
        """设置结果表格"""
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(6)
        self.result_table.setHorizontalHeaderLabels([
            "文件名", "路径", "大小", "修改时间", "扩展名", "状态"
        ])
        
        # 设置表格属性
        header = self.result_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

    def connect_signals(self):
        """连接信号"""
        # 操作按钮信号
        self.operation_buttons_widget.search_clicked.connect(self.search_files)
        self.operation_buttons_widget.copy_clicked.connect(self.copy_files)
        self.operation_buttons_widget.move_clicked.connect(self.move_files)
        self.operation_buttons_widget.delete_clicked.connect(self.delete_files)

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        new_search_action = QAction("新建搜索(&N)", self)
        new_search_action.setShortcut(QKeySequence("Ctrl+N"))
        new_search_action.triggered.connect(self.new_search)
        file_menu.addAction(new_search_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence("Ctrl+Q"))
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        duplicate_stats_action = QAction("重复文件统计(&S)", self)
        duplicate_stats_action.setShortcut(QKeySequence("Ctrl+Shift+S"))
        duplicate_stats_action.triggered.connect(self.show_duplicate_stats)
        tools_menu.addAction(duplicate_stats_action)

        tools_menu.addSeparator()
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        self.status_bar.showMessage("就绪")

    def search_files(self):
        """搜索文件"""
        if self.search_thread and self.search_thread.isRunning():
            self.log_widget.log_message("搜索正在进行中，请等待...")
            return

        # 获取搜索参数
        path = self.search_path_widget.get_path()
        if not Path(path).exists():
            self.log_widget.log_message(f"错误：路径不存在 - {path}")
            QMessageBox.warning(self, "警告", f"路径不存在: {path}")
            return

        self.log_widget.log_message(f"开始搜索文件，目录: {path}")
        self.file_filter = FileFilter(path)

        # 获取筛选条件
        extensions = self.filter_conditions_widget.get_extensions()
        pattern = self.filter_conditions_widget.get_pattern()
        min_size, max_size = self.filter_conditions_widget.get_size_range()
        start_date, end_date = self.filter_conditions_widget.get_date_range()
        recursive = self.search_path_widget.is_recursive()

        # 获取高级筛选条件
        find_duplicates, min_duplicate_count = self.advanced_filter_widget.get_duplicate_settings()
        find_similar_names, min_common_chars, similarity_threshold = self.advanced_filter_widget.get_similar_settings()

        # 获取编号文件筛选条件
        numbered_file_settings = self.numbered_file_widget.get_settings()

        # 创建搜索线程
        self.search_thread = FileSearchThread(
            self.file_filter,
            extensions=extensions,
            name_pattern=pattern,
            min_size=min_size,
            max_size=max_size,
            start_date=start_date,
            end_date=end_date,
            recursive=recursive,
            find_duplicates=find_duplicates,
            min_duplicate_count=min_duplicate_count,
            find_similar_names=find_similar_names,
            min_common_chars=min_common_chars,
            similarity_threshold=similarity_threshold,
            numbered_file_settings=numbered_file_settings
        )

        # 连接信号
        self.search_thread.finished.connect(self.on_search_finished)
        self.search_thread.progress.connect(self.log_widget.log_message)
        self.search_thread.error.connect(self.on_search_error)

        # 开始搜索
        self.log_widget.log_message("正在启动搜索线程...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.operation_buttons_widget.set_enabled(False)
        self.search_thread.start()
        self.log_widget.log_message("搜索线程已启动，正在处理...")

    def on_search_finished(self, files):
        """搜索完成"""
        self.found_files = files
        self.log_widget.log_message(f"搜索完成，找到 {len(files)} 个文件")

        # 更新结果表格
        self.update_result_table()

        # 隐藏进度条
        self.progress_bar.setVisible(False)
        self.operation_buttons_widget.set_enabled(True)

        # 更新状态栏
        self.status_bar.showMessage(f"找到 {len(files)} 个文件")
        self.log_widget.log_message("搜索操作完成")

    def on_search_error(self, error_msg):
        """搜索错误"""
        self.progress_bar.setVisible(False)
        self.operation_buttons_widget.set_enabled(True)
        self.log_widget.log_message(f"搜索失败: {error_msg}")
        QMessageBox.critical(self, "错误", f"搜索失败: {error_msg}")

    def update_result_table(self):
        """更新结果表格"""
        self.result_table.setRowCount(len(self.found_files))
        
        for row, file_path in enumerate(self.found_files):
            try:
                stat = file_path.stat()
                
                # 文件名
                self.result_table.setItem(row, 0, QTableWidgetItem(file_path.name))
                
                # 路径
                self.result_table.setItem(row, 1, QTableWidgetItem(str(file_path.parent)))
                
                # 大小
                size_str = self.format_size(stat.st_size)
                self.result_table.setItem(row, 2, QTableWidgetItem(size_str))
                
                # 修改时间
                from datetime import datetime
                mtime = datetime.fromtimestamp(stat.st_mtime)
                time_str = mtime.strftime("%Y-%m-%d %H:%M:%S")
                self.result_table.setItem(row, 3, QTableWidgetItem(time_str))
                
                # 扩展名
                self.result_table.setItem(row, 4, QTableWidgetItem(file_path.suffix))
                
                # 状态
                self.result_table.setItem(row, 5, QTableWidgetItem("-"))
                
            except OSError:
                # 文件可能已被删除或无法访问
                for col in range(6):
                    self.result_table.setItem(row, col, QTableWidgetItem("无法访问"))

    def format_size(self, size):
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024**2:
            return f"{size/1024:.1f} KB"
        elif size < 1024**3:
            return f"{size/(1024**2):.1f} MB"
        else:
            return f"{size/(1024**3):.1f} GB"

    def copy_files(self):
        """复制文件"""
        if not self.found_files:
            self.log_widget.log_message("警告：请先搜索文件")
            QMessageBox.warning(self, "警告", "请先搜索文件")
            return

        self.log_widget.log_message("正在选择复制目标目录...")
        dest = QFileDialog.getExistingDirectory(self, "选择目标目录")
        if dest:
            self.log_widget.log_message(f"开始复制文件到: {dest}")
            self.perform_file_operation("copy", dest)
        else:
            self.log_widget.log_message("取消复制操作")

    def move_files(self):
        """移动文件"""
        if not self.found_files:
            self.log_widget.log_message("警告：请先搜索文件")
            QMessageBox.warning(self, "警告", "请先搜索文件")
            return

        self.log_widget.log_message("正在选择移动目标目录...")
        dest = QFileDialog.getExistingDirectory(self, "选择目标目录")
        if dest:
            self.log_widget.log_message(f"开始移动文件到: {dest}")
            self.perform_file_operation("move", dest)
        else:
            self.log_widget.log_message("取消移动操作")

    def delete_files(self):
        """删除文件"""
        if not self.found_files:
            self.log_widget.log_message("警告：请先搜索文件")
            QMessageBox.warning(self, "警告", "请先搜索文件")
            return

        self.log_widget.log_message(f"用户请求删除 {len(self.found_files)} 个文件")
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除 {len(self.found_files)} 个文件吗？\n\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.log_widget.log_message("用户确认删除，开始执行删除操作")
            self.perform_file_operation("delete")
        else:
            self.log_widget.log_message("用户取消删除操作")

    def perform_file_operation(self, operation, destination=None):
        """执行文件操作"""
        if self.operation_thread and self.operation_thread.isRunning():
            self.log_widget.log_message("操作正在进行中，请等待...")
            return

        operation_names = {"copy": "复制", "move": "移动", "delete": "删除"}
        op_name = operation_names.get(operation, operation)
        self.log_widget.log_message(f"正在启动{op_name}操作线程...")

        self.operation_thread = FileOperationThread(
            self.file_filter, operation, self.found_files, destination
        )

        # 连接信号
        self.operation_thread.finished.connect(self.on_operation_finished)
        self.operation_thread.progress.connect(self.log_widget.log_message)
        self.operation_thread.error.connect(self.on_operation_error)

        # 开始操作
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.operation_buttons_widget.set_enabled(False)
        self.operation_thread.start()
        self.log_widget.log_message(f"{op_name}操作已开始...")

    def on_operation_finished(self, message):
        """操作完成"""
        self.progress_bar.setVisible(False)
        self.operation_buttons_widget.set_enabled(True)
        self.log_widget.log_message(f"操作完成: {message}")
        QMessageBox.information(self, "成功", message)

        # 如果是移动或删除操作，重新搜索
        if "移动" in message or "删除" in message:
            self.log_widget.log_message("正在重新搜索以更新结果...")
            self.search_files()

    def on_operation_error(self, error_msg):
        """操作错误"""
        self.progress_bar.setVisible(False)
        self.operation_buttons_widget.set_enabled(True)
        self.log_widget.log_message(f"操作失败: {error_msg}")
        QMessageBox.critical(self, "错误", f"操作失败: {error_msg}")

    def new_search(self):
        """新建搜索"""
        self.log_widget.log_message("正在清空搜索结果...")
        self.found_files = []
        self.duplicate_groups = {}
        self.similar_groups = {}
        self.result_table.setRowCount(0)
        self.filter_conditions_widget.clear()
        self.advanced_filter_widget.clear()
        self.numbered_file_widget.clear()
        self.log_widget.log_message("已清空搜索结果和条件")

    def show_duplicate_stats(self):
        """显示重复文件统计"""
        self.log_widget.log_message("显示重复文件统计信息")
        QMessageBox.information(self, "提示", "重复文件统计功能开发中...")

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h3>文件筛选过滤工具 v1.2</h3>
        <p>一个功能强大的文件筛选和管理工具</p>
        <p><b>主要功能：</b></p>
        <ul>
        <li>多种筛选条件</li>
        <li>重复文件检测</li>
        <li>文件名相似度检测</li>
        <li>文件操作（复制、移动、删除）</li>
        <li>现代化界面</li>
        </ul>
        <p><b>开发者：</b>Andy_127【浅醉丶墨语】</p>
        <p><b>联系方式：</b>yangjun_127</p>
        """
        QMessageBox.about(self, "关于", about_text)

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("错误: 需要安装PySide6")
        print("请运行: pip install PySide6")
        return

    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("文件筛选过滤工具")
    app.setApplicationVersion("1.2")
    app.setOrganizationName("Andy_127")
    
    # 设置应用程序图标
    try:
        project_root = Path(__file__).parent.parent.parent
        icon_path = project_root / "assets" / "res" / "logo.ico"
        if icon_path.exists():
            icon = QIcon(str(icon_path))
            app.setWindowIcon(icon)
    except:
        pass

    window = FileFilterMainWindow()
    window.show()

    sys.exit(app.exec())

if __name__ == '__main__':
    main()
