#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选过滤工具 - 无控制台启动脚本
File Filter Tool - No Console Launcher Script
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def show_error(message):
    """显示错误信息"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动错误", message)
        root.destroy()
    except ImportError:
        # 如果tkinter不可用，写入错误日志
        error_log = Path(__file__).parent.parent / "error.log"
        with open(error_log, "w", encoding="utf-8") as f:
            f.write(f"启动错误: {message}\n")

def main():
    """主函数"""
    try:
        # 启动重构后的PySide6版本
        from file_filter_tool.gui_main import main as gui_main
        gui_main()
    except ImportError as e:
        show_error(f"无法导入PySide6模块\n请确保已安装PySide6：pip install PySide6\n\n详细错误：{e}")
        return False
    except Exception as e:
        show_error(f"启动失败：{e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
