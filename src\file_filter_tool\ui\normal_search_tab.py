#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通搜索标签页
包含原有的文件搜索和筛选功能
"""

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
        QGroupBox, QPushButton, QTableWidget, QTableWidgetItem,
        QHeaderView, QProgressBar, QTextEdit, QLabel
    )
    from PySide6.QtCore import Qt, Signal
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

from .components import (
    SearchPathWidget, FilterConditionsWidget, 
    NumberedFileFilterWidget, OperationButtonsWidget
)


class NormalSearchTab(QWidget):
    """普通搜索标签页"""
    
    # 信号定义
    status_message = Signal(str)
    progress_update = Signal(int, int, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.found_files = []
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：搜索设置
        left_widget = self.create_search_settings_widget()
        main_splitter.addWidget(left_widget)
        
        # 右侧：结果显示
        right_widget = self.create_results_widget()
        main_splitter.addWidget(right_widget)
        
        # 设置分割器比例
        main_splitter.setSizes([400, 800])
        layout.addWidget(main_splitter)
    
    def create_search_settings_widget(self):
        """创建搜索设置控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 搜索路径
        path_group = QGroupBox("📁 搜索路径")
        path_layout = QVBoxLayout(path_group)
        self.search_path_widget = SearchPathWidget()
        path_layout.addWidget(self.search_path_widget)
        layout.addWidget(path_group)
        
        # 筛选条件
        filter_group = QGroupBox("🔍 筛选条件")
        filter_layout = QVBoxLayout(filter_group)
        self.filter_conditions_widget = FilterConditionsWidget()
        filter_layout.addWidget(self.filter_conditions_widget)
        layout.addWidget(filter_group)
        
        # 编号文件筛选
        numbered_group = QGroupBox("📝 编号文件筛选")
        numbered_layout = QVBoxLayout(numbered_group)
        self.numbered_file_widget = NumberedFileFilterWidget()
        numbered_layout.addWidget(self.numbered_file_widget)
        layout.addWidget(numbered_group)
        
        # 操作按钮
        self.operation_buttons = OperationButtonsWidget()
        self.operation_buttons.set_mode("normal")
        layout.addWidget(self.operation_buttons)
        
        layout.addStretch()
        return widget
    
    def create_results_widget(self):
        """创建结果显示控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 结果统计
        stats_layout = QHBoxLayout()
        self.stats_label = QLabel("📊 搜索结果: 0 个文件")
        self.stats_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        stats_layout.addWidget(self.stats_label)
        
        stats_layout.addStretch()
        
        # 快速操作按钮
        self.export_btn = QPushButton("📋 导出结果")
        self.export_btn.setMaximumWidth(100)
        stats_layout.addWidget(self.export_btn)
        
        self.clear_btn = QPushButton("🔄 清空结果")
        self.clear_btn.setMaximumWidth(100)
        stats_layout.addWidget(self.clear_btn)
        
        layout.addLayout(stats_layout)
        
        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(6)
        self.result_table.setHorizontalHeaderLabels([
            "文件名", "路径", "大小", "修改时间", "扩展名", "状态"
        ])
        
        # 设置表格属性
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 文件名
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)           # 路径
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 大小
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 扩展名
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.result_table.setSortingEnabled(True)
        
        layout.addWidget(self.result_table)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("📝 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return widget
    
    def connect_signals(self):
        """连接信号"""
        # 操作按钮信号
        self.operation_buttons.search_clicked.connect(self.start_search)
        self.operation_buttons.copy_clicked.connect(self.copy_files)
        self.operation_buttons.move_clicked.connect(self.move_files)
        self.operation_buttons.delete_clicked.connect(self.delete_files)
        
        # 快速操作按钮信号
        self.export_btn.clicked.connect(self.export_results)
        self.clear_btn.clicked.connect(self.clear_results)
        
        # 搜索路径变化信号
        self.search_path_widget.path_changed.connect(self.on_path_changed)
    
    def start_search(self):
        """开始搜索"""
        self.log_message("🔍 开始普通文件搜索...")
        self.status_message.emit("正在搜索文件...")
        
        # 获取搜索设置
        search_path = self.search_path_widget.get_path()
        if not search_path:
            self.log_message("❌ 请选择搜索路径")
            return
        
        # 这里应该调用实际的搜索逻辑
        # 暂时模拟搜索过程
        self.simulate_search()
    
    def simulate_search(self):
        """模拟搜索过程（用于测试）"""
        import time
        from pathlib import Path
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        
        # 模拟搜索进度
        for i in range(101):
            self.progress_bar.setValue(i)
            self.progress_update.emit(i, 100, f"搜索进度: {i}%")
            time.sleep(0.01)  # 模拟搜索时间
        
        # 模拟搜索结果
        search_path = Path(self.search_path_widget.get_path())
        if search_path.exists():
            self.found_files = list(search_path.rglob("*"))[:50]  # 限制显示50个文件
            self.display_results()
        
        self.progress_bar.setVisible(False)
        self.log_message(f"✅ 搜索完成，找到 {len(self.found_files)} 个文件")
        self.status_message.emit(f"搜索完成，找到 {len(self.found_files)} 个文件")
    
    def display_results(self):
        """显示搜索结果"""
        self.result_table.setRowCount(len(self.found_files))
        
        for row, file_path in enumerate(self.found_files):
            if not file_path.is_file():
                continue
                
            try:
                stat = file_path.stat()
                
                # 文件名
                self.result_table.setItem(row, 0, QTableWidgetItem(file_path.name))
                
                # 路径
                self.result_table.setItem(row, 1, QTableWidgetItem(str(file_path.parent)))
                
                # 大小
                size_str = self.format_size(stat.st_size)
                self.result_table.setItem(row, 2, QTableWidgetItem(size_str))
                
                # 修改时间
                from datetime import datetime
                mtime = datetime.fromtimestamp(stat.st_mtime)
                time_str = mtime.strftime("%Y-%m-%d %H:%M:%S")
                self.result_table.setItem(row, 3, QTableWidgetItem(time_str))
                
                # 扩展名
                self.result_table.setItem(row, 4, QTableWidgetItem(file_path.suffix))
                
                # 状态
                self.result_table.setItem(row, 5, QTableWidgetItem("正常"))
                
            except OSError:
                # 文件可能已被删除或无法访问
                for col in range(6):
                    self.result_table.setItem(row, col, QTableWidgetItem("无法访问"))
        
        # 更新统计信息
        self.stats_label.setText(f"📊 搜索结果: {len(self.found_files)} 个文件")
    
    def copy_files(self):
        """复制文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            self.log_message("❌ 请先选择要复制的文件")
            return
        
        self.log_message(f"📋 准备复制 {len(selected_files)} 个文件...")
        self.status_message.emit(f"准备复制 {len(selected_files)} 个文件")
    
    def move_files(self):
        """移动文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            self.log_message("❌ 请先选择要移动的文件")
            return
        
        self.log_message(f"📁 准备移动 {len(selected_files)} 个文件...")
        self.status_message.emit(f"准备移动 {len(selected_files)} 个文件")
    
    def delete_files(self):
        """删除文件"""
        selected_files = self.get_selected_files()
        if not selected_files:
            self.log_message("❌ 请先选择要删除的文件")
            return
        
        self.log_message(f"🗑️ 准备删除 {len(selected_files)} 个文件...")
        self.status_message.emit(f"准备删除 {len(selected_files)} 个文件")
    
    def export_results(self):
        """导出结果"""
        if not self.found_files:
            self.log_message("❌ 没有可导出的结果")
            return
        
        self.log_message("📋 导出搜索结果...")
        self.status_message.emit("正在导出结果...")
    
    def clear_results(self):
        """清空结果"""
        self.result_table.setRowCount(0)
        self.found_files.clear()
        self.stats_label.setText("📊 搜索结果: 0 个文件")
        self.log_message("🔄 已清空搜索结果")
        self.status_message.emit("已清空搜索结果")
    
    def get_selected_files(self):
        """获取选中的文件"""
        selected_rows = set()
        for item in self.result_table.selectedItems():
            selected_rows.add(item.row())
        
        selected_files = []
        for row in selected_rows:
            if row < len(self.found_files):
                selected_files.append(self.found_files[row])
        
        return selected_files
    
    def on_path_changed(self, path):
        """路径变化处理"""
        self.log_message(f"📁 搜索路径已更改: {path}")
        self.status_message.emit(f"搜索路径: {path}")
    
    def log_message(self, message):
        """记录日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        self.log_text.ensureCursorVisible()
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def get_settings(self):
        """获取当前设置"""
        return {
            "search_path": self.search_path_widget.get_path(),
            "filter_conditions": self.filter_conditions_widget.get_filter_conditions(),
            "numbered_file_settings": self.numbered_file_widget.get_settings()
        }
    
    def load_settings(self, settings):
        """加载设置"""
        if "search_path" in settings:
            self.search_path_widget.set_path(settings["search_path"])
        # 可以继续加载其他设置...
    
    def clear_settings(self):
        """清空设置"""
        self.filter_conditions_widget.clear()
        self.numbered_file_widget.clear()
        self.clear_results()
        self.log_message("🔄 已清空所有设置")
