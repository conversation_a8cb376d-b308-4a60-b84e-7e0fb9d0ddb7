#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新的标签页主窗口
使用标签页界面 + 双目录对比分屏布局的混合方案
"""

import sys
from pathlib import Path

try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout,
        QMenuBar, QStatusBar, QProgressBar, QMessageBox,
        QFileDialog, QAction
    )
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QIcon, QKeySequence
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

from .ui.main_tabbed_interface import MainTabbedInterface
from .ui.themes import WindowsTheme
from .ui.help_dialog import HelpDialog


class TabbedFileFilterMainWindow(QMainWindow):
    """新的标签页主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.apply_theme()
        self.connect_signals()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("智能文件管理工具 v2.0 - 标签页界面")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # 设置窗口图标
        try:
            icon_path = Path(__file__).parent / "resources" / "icon.ico"
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            print(f"设置图标时出错: {e}")
    
    def setup_ui(self):
        """设置UI"""
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建主标签页界面
        self.main_interface = MainTabbedInterface()
        layout.addWidget(self.main_interface)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence("Ctrl+N"))
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        # 导入设置
        import_action = QAction("导入设置(&I)", self)
        import_action.setShortcut(QKeySequence("Ctrl+I"))
        import_action.triggered.connect(self.import_settings)
        file_menu.addAction(import_action)
        
        # 导出设置
        export_action = QAction("导出设置(&E)", self)
        export_action.setShortcut(QKeySequence("Ctrl+E"))
        export_action.triggered.connect(self.export_settings)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence("Ctrl+Q"))
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 切换标签页
        switch_normal_action = QAction("普通搜索(&1)", self)
        switch_normal_action.setShortcut(QKeySequence("Ctrl+1"))
        switch_normal_action.triggered.connect(lambda: self.main_interface.switch_to_tab("normal"))
        view_menu.addAction(switch_normal_action)
        
        switch_dual_action = QAction("双目录对比(&2)", self)
        switch_dual_action.setShortcut(QKeySequence("Ctrl+2"))
        switch_dual_action.triggered.connect(lambda: self.main_interface.switch_to_tab("dual_directory"))
        view_menu.addAction(switch_dual_action)
        
        switch_duplicate_action = QAction("重复清理(&3)", self)
        switch_duplicate_action.setShortcut(QKeySequence("Ctrl+3"))
        switch_duplicate_action.triggered.connect(lambda: self.main_interface.switch_to_tab("duplicate"))
        view_menu.addAction(switch_duplicate_action)
        
        switch_batch_action = QAction("批量操作(&4)", self)
        switch_batch_action.setShortcut(QKeySequence("Ctrl+4"))
        switch_batch_action.triggered.connect(lambda: self.main_interface.switch_to_tab("batch"))
        view_menu.addAction(switch_batch_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 清空所有设置
        clear_action = QAction("清空所有设置(&C)", self)
        clear_action.setShortcut(QKeySequence("Ctrl+Shift+C"))
        clear_action.triggered.connect(self.clear_all_settings)
        tools_menu.addAction(clear_action)
        
        tools_menu.addSeparator()
        
        # 系统信息
        system_info_action = QAction("系统信息(&S)", self)
        system_info_action.triggered.connect(self.show_system_info)
        tools_menu.addAction(system_info_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 使用帮助
        usage_help_action = QAction("使用帮助(&U)", self)
        usage_help_action.setShortcut(QKeySequence("F1"))
        usage_help_action.triggered.connect(self.show_usage_help)
        help_menu.addAction(usage_help_action)
        
        # 快捷键帮助
        shortcut_help_action = QAction("快捷键帮助(&K)", self)
        shortcut_help_action.setShortcut(QKeySequence("Ctrl+?"))
        shortcut_help_action.triggered.connect(self.show_shortcut_help)
        help_menu.addAction(shortcut_help_action)
        
        help_menu.addSeparator()
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        self.status_bar.showMessage("就绪 - 智能文件管理工具")
    
    def apply_theme(self):
        """应用主题"""
        style = WindowsTheme.get_complete_style()
        self.setStyleSheet(style)
    
    def connect_signals(self):
        """连接信号"""
        # 连接主界面的状态信号
        self.main_interface.status_message.connect(self.update_status_message)
        self.main_interface.progress_update.connect(self.update_progress)
    
    def update_status_message(self, message):
        """更新状态栏消息"""
        self.status_bar.showMessage(message)
    
    def update_progress(self, current, total, message):
        """更新进度条"""
        if total > 0:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, total)
            self.progress_bar.setValue(current)
            self.status_bar.showMessage(f"{message} ({current}/{total})")
        else:
            self.progress_bar.setVisible(False)
    
    def new_project(self):
        """新建项目"""
        reply = QMessageBox.question(
            self, "新建项目",
            "确定要清空所有设置并开始新项目吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.main_interface.clear_all_tabs()
            self.update_status_message("已创建新项目")
    
    def import_settings(self):
        """导入设置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入设置文件",
            str(Path.home()),
            "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            success = self.main_interface.import_settings(file_path)
            if success:
                QMessageBox.information(self, "导入成功", "设置已成功导入！")
            else:
                QMessageBox.warning(self, "导入失败", "设置导入失败，请检查文件格式。")
    
    def export_settings(self):
        """导出设置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出设置文件",
            str(Path.home() / "file_filter_settings.json"),
            "JSON文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            success = self.main_interface.export_settings(file_path)
            if success:
                QMessageBox.information(self, "导出成功", f"设置已导出到:\n{file_path}")
            else:
                QMessageBox.warning(self, "导出失败", "设置导出失败。")
    
    def clear_all_settings(self):
        """清空所有设置"""
        reply = QMessageBox.question(
            self, "清空设置",
            "确定要清空所有标签页的设置吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.main_interface.clear_all_tabs()
            QMessageBox.information(self, "清空完成", "所有设置已清空！")
    
    def show_system_info(self):
        """显示系统信息"""
        import platform
        import psutil
        
        info = f"""
        <h3>系统信息</h3>
        <p><b>操作系统:</b> {platform.system()} {platform.release()}</p>
        <p><b>Python版本:</b> {platform.python_version()}</p>
        <p><b>CPU核心数:</b> {psutil.cpu_count()}</p>
        <p><b>内存:</b> {psutil.virtual_memory().total // (1024**3)} GB</p>
        <p><b>磁盘空间:</b> {psutil.disk_usage('/').total // (1024**3)} GB</p>
        """
        
        QMessageBox.information(self, "系统信息", info)
    
    def show_usage_help(self):
        """显示使用帮助"""
        help_dialog = HelpDialog(self)
        help_dialog.exec()
    
    def show_shortcut_help(self):
        """显示快捷键帮助"""
        shortcuts = """
        <h3>快捷键帮助</h3>
        <table>
        <tr><td><b>Ctrl+N</b></td><td>新建项目</td></tr>
        <tr><td><b>Ctrl+I</b></td><td>导入设置</td></tr>
        <tr><td><b>Ctrl+E</b></td><td>导出设置</td></tr>
        <tr><td><b>Ctrl+Q</b></td><td>退出程序</td></tr>
        <tr><td><b>Ctrl+1</b></td><td>切换到普通搜索</td></tr>
        <tr><td><b>Ctrl+2</b></td><td>切换到双目录对比</td></tr>
        <tr><td><b>Ctrl+3</b></td><td>切换到重复清理</td></tr>
        <tr><td><b>Ctrl+4</b></td><td>切换到批量操作</td></tr>
        <tr><td><b>Ctrl+Shift+C</b></td><td>清空所有设置</td></tr>
        <tr><td><b>F1</b></td><td>使用帮助</td></tr>
        <tr><td><b>Ctrl+?</b></td><td>快捷键帮助</td></tr>
        </table>
        """
        
        QMessageBox.information(self, "快捷键帮助", shortcuts)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
        <h3>智能文件管理工具 v2.0</h3>
        <p>一个功能强大的文件管理和整理工具</p>
        
        <p><b>新版本特性：</b></p>
        <ul>
        <li>🔍 <b>普通搜索</b> - 强大的文件搜索和筛选功能</li>
        <li>🔄 <b>双目录对比</b> - 专业的分屏对比界面</li>
        <li>🗂️ <b>重复清理</b> - 智能重复文件检测和清理</li>
        <li>📊 <b>批量操作</b> - 批量重命名、转换、分析、整理</li>
        </ul>
        
        <p><b>界面优势：</b></p>
        <ul>
        <li>标签页设计，功能分离清晰</li>
        <li>专业的分屏对比界面</li>
        <li>现代化的UI设计</li>
        <li>丰富的快捷键支持</li>
        </ul>
        
        <p><b>开发信息：</b></p>
        <p>基于 PySide6 开发，支持 Windows 系统</p>
        <p>版权所有 © 2024</p>
        """
        
        QMessageBox.about(self, "关于", about_text)
    
    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(
            self, "确认退出",
            "确定要退出智能文件管理工具吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()


def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("错误：需要安装PySide6")
        print("请运行: pip install PySide6")
        return False
    
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("智能文件管理工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("FileFilter")
    
    # 创建主窗口
    window = TabbedFileFilterMainWindow()
    window.show()
    
    return app.exec()


if __name__ == '__main__':
    sys.exit(main())
