#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布局测试脚本
测试不同窗口大小下的界面表现
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QLabel
    from PySide6.QtCore import QTimer
    from file_filter_tool.gui_main import FileFilterMainWindow
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

def test_window_sizes():
    """测试不同窗口大小"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行布局测试")
        return False
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = FileFilterMainWindow()
    
    # 测试不同尺寸
    test_sizes = [
        (1000, 700, "最小尺寸"),
        (1200, 800, "中等尺寸"),
        (1300, 850, "默认尺寸"),
        (1600, 1000, "大尺寸"),
    ]
    
    print("🧪 开始布局测试...")
    print("将依次显示不同尺寸的窗口，每个持续3秒")
    
    current_test = 0
    
    def next_test():
        nonlocal current_test
        if current_test < len(test_sizes):
            width, height, name = test_sizes[current_test]
            print(f"📐 测试 {name}: {width}x{height}")
            window.resize(width, height)
            window.show()
            current_test += 1
            
            # 3秒后进行下一个测试
            QTimer.singleShot(3000, next_test)
        else:
            print("✅ 布局测试完成")
            print("💡 如果所有尺寸下界面都正常显示，说明布局优化成功")
            app.quit()
    
    # 开始测试
    next_test()
    
    return app.exec()

def create_test_widget():
    """创建测试控件"""
    if not PYSIDE6_AVAILABLE:
        return None
    
    widget = QWidget()
    layout = QVBoxLayout(widget)
    
    layout.addWidget(QLabel("布局测试"))
    
    btn = QPushButton("开始测试")
    btn.clicked.connect(test_window_sizes)
    layout.addWidget(btn)
    
    return widget

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行布局测试")
        print("请运行: pip install PySide6")
        return False
    
    print("🚀 文件筛选工具 - 布局测试")
    print("=" * 40)
    
    try:
        result = test_window_sizes()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
