#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编号文件筛选测试脚本
测试同编号文件的时间筛选功能
"""

import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from file_filter_tool.core.numbered_files import (
        NumberedFileDetector, 
        NumberedFileGrouper, 
        TimeFilterStrategy, 
        NumberedFileFilter
    )
    MODULES_AVAILABLE = True
except ImportError as e:
    MODULES_AVAILABLE = False
    print(f"模块导入失败: {e}")

def create_test_files():
    """创建测试文件"""
    # 创建临时目录
    temp_dir = Path(tempfile.mkdtemp())
    print(f"创建测试目录: {temp_dir}")
    
    # 创建测试文件列表
    test_files = [
        # 报告文件组
        ("report_001.pdf", 1),   # 最旧
        ("report_002.pdf", 5),   # 中间
        ("report_003.pdf", 10),  # 最新
        
        # 图片文件组
        ("image_001.jpg", 2),    # 最旧
        ("image_002.jpg", 8),    # 最新
        
        # 版本文件组
        ("app_v1.0.exe", 3),     # 最旧
        ("app_v1.1.exe", 6),     # 中间
        ("app_v2.0.exe", 9),     # 最新
        
        # 备份文件组
        ("backup_20240101.zip", 4),  # 最旧
        ("backup_20240105.zip", 7),  # 最新
        
        # 非编号文件
        ("readme.txt", 11),
        ("config.ini", 12),
    ]
    
    # 创建文件并设置修改时间
    base_time = datetime.now() - timedelta(days=20)
    created_files = []
    
    for filename, days_offset in test_files:
        file_path = temp_dir / filename
        
        # 创建文件
        file_path.write_text(f"这是测试文件: {filename}")
        
        # 设置修改时间
        file_time = base_time + timedelta(days=days_offset)
        timestamp = file_time.timestamp()

        # 使用os.utime设置文件时间（兼容性更好）
        import os
        os.utime(file_path, (timestamp, timestamp))
        
        created_files.append(file_path)
        print(f"创建文件: {filename} (时间: {file_time.strftime('%Y-%m-%d')})")
    
    return temp_dir, created_files

def test_number_detection():
    """测试编号检测"""
    print("\n🧪 测试编号检测功能")
    print("=" * 40)
    
    detector = NumberedFileDetector()
    
    test_cases = [
        "report_001.pdf",
        "image_002.jpg", 
        "app_v1.0.exe",
        "backup_20240101.zip",
        "readme.txt",  # 无编号
        "001_document.doc",  # 前缀编号
        "file_001_final.txt",  # 中间编号
    ]
    
    for filename in test_cases:
        pattern = detector.detect_pattern(filename)
        if pattern:
            print(f"✅ {filename} -> {pattern['type']}: '{pattern['base_name']}' + '{pattern['number']}'")
        else:
            print(f"❌ {filename} -> 未检测到编号")
    
    return True

def test_file_grouping():
    """测试文件分组"""
    print("\n🧪 测试文件分组功能")
    print("=" * 40)
    
    temp_dir, files = create_test_files()
    
    try:
        detector = NumberedFileDetector()
        grouper = NumberedFileGrouper(detector)
        
        # 分组文件
        groups = grouper.group_files(files)
        
        print(f"检测到 {len(groups)} 个编号文件组:")
        for group_name, file_list in groups.items():
            print(f"\n📁 分组: '{group_name}' ({len(file_list)} 个文件)")
            for file_info in sorted(file_list, key=lambda x: x['mtime']):
                print(f"  - {file_info['filename']} ({file_info['mtime_str']})")
        
        return True
        
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir)

def test_time_strategies():
    """测试时间筛选策略"""
    print("\n🧪 测试时间筛选策略")
    print("=" * 40)
    
    temp_dir, files = create_test_files()
    
    try:
        numbered_filter = NumberedFileFilter()
        
        strategies = [
            ("keep_newest", "保留最新", {}),
            ("keep_oldest", "保留最旧", {}),
            ("keep_newest_n", "保留最新2个", {"count": 2}),
            ("keep_by_interval", "按3天间隔保留", {"interval_days": 3}),
        ]
        
        for strategy, name, options in strategies:
            print(f"\n📋 策略: {name}")
            
            filtered_files, stats = numbered_filter.filter_numbered_files(
                files,
                time_strategy=strategy,
                strategy_options=options
            )
            
            print(f"  输入文件: {stats['total_input_files']} 个")
            print(f"  编号文件组: {stats['numbered_groups']} 个")
            print(f"  保留文件: {stats['total_kept_files']} 个")
            print(f"  过滤文件: {stats['total_filtered_files']} 个")
            
            # 显示保留的文件
            print("  保留的文件:")
            for file_path in sorted(filtered_files, key=lambda x: x.name):
                try:
                    mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    print(f"    ✅ {file_path.name} ({mtime.strftime('%Y-%m-%d')})")
                except:
                    print(f"    ✅ {file_path.name}")
        
        return True
        
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir)

def test_ui_integration():
    """测试UI集成"""
    print("\n🧪 测试UI集成")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication
        from file_filter_tool.ui.components import NumberedFileFilterWidget
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建组件
        widget = NumberedFileFilterWidget()
        
        # 测试默认设置
        settings = widget.get_settings()
        if settings is None:
            print("✅ 默认状态: 功能未启用")
        
        # 启用功能并测试设置
        widget.enable_check.setChecked(True)
        widget.strategy_combo.setCurrentText("保留最新N个")
        widget.count_spin.setValue(3)
        
        settings = widget.get_settings()
        if settings and settings['enabled']:
            print("✅ 启用状态: 功能已启用")
            print(f"  策略: {settings['strategy']}")
            print(f"  选项: {settings['options']}")
            print(f"  忽略扩展名: {settings['ignore_extension']}")
            print(f"  区分大小写: {settings['case_sensitive']}")
        
        # 测试清空功能
        widget.clear()
        settings = widget.get_settings()
        if settings is None:
            print("✅ 清空功能: 正常")
        
        return True
        
    except ImportError:
        print("❌ PySide6不可用，跳过UI测试")
        return True
    except Exception as e:
        print(f"❌ UI测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 编号文件筛选功能测试")
    print("=" * 50)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块导入失败，无法进行测试")
        return False
    
    tests = [
        ("编号检测", test_number_detection),
        ("文件分组", test_file_grouping), 
        ("时间策略", test_time_strategies),
        ("UI集成", test_ui_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！编号文件筛选功能正常！")
        return True
    else:
        print("❌ 部分测试失败，需要检查功能实现")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
