#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双目录文件对比器
对比两个子目录下的文件，找出相同文件并保留最新版本
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime
import logging
from collections import defaultdict


class DualDirectoryComparator:
    """双目录文件对比器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # 匹配模式
        self.MATCH_EXACT = "exact"           # 完全匹配
        self.MATCH_IGNORE_EXT = "ignore_ext" # 忽略扩展名
        self.MATCH_NUMBER = "number"         # 编号匹配
        
        # 时间模式
        self.TIME_MODIFIED = "modified"      # 修改时间
        self.TIME_CREATED = "created"        # 创建时间
        self.TIME_ACCESSED = "accessed"      # 访问时间
        
        # 处理模式
        self.ACTION_SHOW_ONLY = "show_only"     # 仅显示
        self.ACTION_KEEP_NEWER = "keep_newer"   # 保留最新
        self.ACTION_BACKUP_OLDER = "backup_older" # 备份旧文件
    
    def compare_directories(self, root_path: Path, subdir1: str, subdir2: str, 
                          match_mode: str = "exact", time_mode: str = "modified",
                          ignore_case: bool = True, recursive: bool = True) -> Dict[str, Any]:
        """
        对比两个子目录下的文件
        
        Args:
            root_path: 根目录路径
            subdir1: 子目录1名称
            subdir2: 子目录2名称
            match_mode: 匹配模式
            time_mode: 时间比较模式
            ignore_case: 是否忽略大小写
            recursive: 是否递归搜索
            
        Returns:
            对比结果字典
        """
        self.logger.info(f"开始对比目录: {subdir1} vs {subdir2}")
        
        # 构建子目录路径
        dir1_path = root_path / subdir1
        dir2_path = root_path / subdir2
        
        # 检查目录是否存在
        if not dir1_path.exists():
            raise FileNotFoundError(f"子目录不存在: {dir1_path}")
        if not dir2_path.exists():
            raise FileNotFoundError(f"子目录不存在: {dir2_path}")
        
        # 扫描两个目录下的文件
        files1 = self._scan_directory(dir1_path, recursive)
        files2 = self._scan_directory(dir2_path, recursive)
        
        self.logger.info(f"目录1文件数: {len(files1)}, 目录2文件数: {len(files2)}")
        
        # 查找匹配的文件对
        file_pairs = self._find_matching_files(files1, files2, match_mode, ignore_case)
        
        # 分析文件对的时间关系
        comparison_results = self._analyze_file_pairs(file_pairs, time_mode)
        
        # 生成统计信息
        statistics = self._generate_statistics(files1, files2, file_pairs, comparison_results)
        
        return {
            'dir1_path': dir1_path,
            'dir2_path': dir2_path,
            'files1': files1,
            'files2': files2,
            'file_pairs': file_pairs,
            'comparison_results': comparison_results,
            'statistics': statistics
        }
    
    def _scan_directory(self, directory: Path, recursive: bool = True) -> List[Path]:
        """扫描目录下的所有文件"""
        files = []
        
        try:
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    files.append(file_path)
                    
        except (OSError, PermissionError) as e:
            self.logger.warning(f"扫描目录失败 {directory}: {e}")
        
        return files
    
    def _find_matching_files(self, files1: List[Path], files2: List[Path], 
                           match_mode: str, ignore_case: bool) -> List[Tuple[Path, Path]]:
        """查找匹配的文件对"""
        file_pairs = []
        
        # 为files2建立索引以提高查找效率
        files2_index = self._build_file_index(files2, match_mode, ignore_case)
        
        for file1 in files1:
            match_key = self._get_match_key(file1, match_mode, ignore_case)
            
            if match_key in files2_index:
                for file2 in files2_index[match_key]:
                    file_pairs.append((file1, file2))
                    self.logger.debug(f"找到匹配文件对: {file1.name} <-> {file2.name}")
        
        self.logger.info(f"找到 {len(file_pairs)} 对匹配文件")
        return file_pairs
    
    def _build_file_index(self, files: List[Path], match_mode: str, ignore_case: bool) -> Dict[str, List[Path]]:
        """为文件列表建立索引"""
        index = defaultdict(list)
        
        for file_path in files:
            match_key = self._get_match_key(file_path, match_mode, ignore_case)
            index[match_key].append(file_path)
        
        return index
    
    def _get_match_key(self, file_path: Path, match_mode: str, ignore_case: bool) -> str:
        """获取文件的匹配键"""
        if match_mode == self.MATCH_EXACT:
            # 完全匹配文件名
            key = file_path.name
            
        elif match_mode == self.MATCH_IGNORE_EXT:
            # 忽略扩展名匹配
            key = file_path.stem
            
        elif match_mode == self.MATCH_NUMBER:
            # 编号匹配 - 提取文件名中的编号部分
            key = self._extract_number_pattern(file_path.name)
            
        else:
            key = file_path.name
        
        return key.lower() if ignore_case else key
    
    def _extract_number_pattern(self, filename: str) -> str:
        """提取文件名中的编号模式"""
        # 移除扩展名
        name_without_ext = Path(filename).stem
        
        # 尝试匹配常见的编号模式
        patterns = [
            r'(.+?)_(\d+)$',      # 文件名_001
            r'(.+?)\.(\d+)$',     # 文件名.001  
            r'(.+?)\((\d+)\)$',   # 文件名(001)
            r'(.+?)-(\d+)$',      # 文件名-001
            r'(.+?)\s+(\d+)$',    # 文件名 001
        ]
        
        for pattern in patterns:
            match = re.match(pattern, name_without_ext)
            if match:
                base_name, number = match.groups()
                # 返回基础名称，用于匹配
                return base_name.strip()
        
        # 如果没有匹配到编号模式，返回原文件名（不含扩展名）
        return name_without_ext
    
    def _analyze_file_pairs(self, file_pairs: List[Tuple[Path, Path]], 
                          time_mode: str) -> List[Dict[str, Any]]:
        """分析文件对的时间关系"""
        results = []
        
        for file1, file2 in file_pairs:
            try:
                # 获取文件时间戳
                time1 = self._get_file_time(file1, time_mode)
                time2 = self._get_file_time(file2, time_mode)
                
                # 比较时间
                if time1 > time2:
                    newer_file = file1
                    older_file = file2
                    time_diff = time1 - time2
                elif time2 > time1:
                    newer_file = file2
                    older_file = file1
                    time_diff = time2 - time1
                else:
                    # 时间相同
                    newer_file = file1  # 默认选择第一个
                    older_file = file2
                    time_diff = 0
                
                # 获取文件大小
                size1 = file1.stat().st_size
                size2 = file2.stat().st_size
                
                result = {
                    'file1': file1,
                    'file2': file2,
                    'newer_file': newer_file,
                    'older_file': older_file,
                    'time1': time1,
                    'time2': time2,
                    'time_diff': time_diff,
                    'size1': size1,
                    'size2': size2,
                    'size_diff': abs(size1 - size2),
                    'time_mode': time_mode
                }
                
                results.append(result)
                
            except (OSError, IOError) as e:
                self.logger.warning(f"分析文件对失败 {file1} <-> {file2}: {e}")
                continue
        
        return results
    
    def _get_file_time(self, file_path: Path, time_mode: str) -> float:
        """获取文件的时间戳"""
        stat = file_path.stat()
        
        if time_mode == self.TIME_MODIFIED:
            return stat.st_mtime
        elif time_mode == self.TIME_CREATED:
            return stat.st_ctime
        elif time_mode == self.TIME_ACCESSED:
            return stat.st_atime
        else:
            return stat.st_mtime  # 默认使用修改时间
    
    def _generate_statistics(self, files1: List[Path], files2: List[Path], 
                           file_pairs: List[Tuple[Path, Path]], 
                           comparison_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成统计信息"""
        total_files1 = len(files1)
        total_files2 = len(files2)
        matched_pairs = len(file_pairs)
        
        # 计算独有文件
        matched_files1 = {pair[0] for pair in file_pairs}
        matched_files2 = {pair[1] for pair in file_pairs}
        unique_files1 = [f for f in files1 if f not in matched_files1]
        unique_files2 = [f for f in files2 if f not in matched_files2]
        
        # 计算可节省的空间
        total_older_size = 0
        for result in comparison_results:
            try:
                older_size = result['older_file'].stat().st_size
                total_older_size += older_size
            except OSError:
                continue
        
        return {
            'total_files1': total_files1,
            'total_files2': total_files2,
            'matched_pairs': matched_pairs,
            'unique_files1': len(unique_files1),
            'unique_files2': len(unique_files2),
            'unique_files1_list': unique_files1,
            'unique_files2_list': unique_files2,
            'total_older_size': total_older_size,
            'space_saving': self._format_size(total_older_size)
        }
    
    def get_files_to_process(self, comparison_results: List[Dict[str, Any]], 
                           action: str) -> List[Path]:
        """
        根据处理模式获取需要处理的文件列表
        
        Args:
            comparison_results: 对比结果
            action: 处理动作
            
        Returns:
            需要处理的文件列表
        """
        files_to_process = []
        
        for result in comparison_results:
            if action == self.ACTION_SHOW_ONLY:
                # 显示所有匹配的文件
                files_to_process.extend([result['file1'], result['file2']])
            elif action == self.ACTION_KEEP_NEWER:
                # 返回需要删除的旧文件
                files_to_process.append(result['older_file'])
            elif action == self.ACTION_BACKUP_OLDER:
                # 返回需要备份的旧文件
                files_to_process.append(result['older_file'])
        
        return files_to_process
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
