# 文件筛选工具 - 项目结构说明

## 📁 项目文件结构

```
文件筛选工具/
├── 📄 核心功能文件
│   ├── file_filter.py      # 核心文件筛选逻辑
│   ├── config.py           # 配置管理模块
│   └── main.py             # 命令行入口程序
│
├── 🖥️ 图形界面文件
│   ├── gui.py              # tkinter传统界面
│   └── gui_pyside6.py      # PySide6现代化界面 ⭐
│
├── 🚀 启动和工具文件
│   ├── 启动GUI.py          # 智能GUI启动器 ⭐
│   ├── 检查依赖.py         # 依赖检查工具
│   ├── demo.py             # 功能演示程序
│   └── test_file_filter.py # 单元测试文件
│
├── 📋 配置和文档文件
│   ├── requirements.txt    # Python依赖列表
│   ├── sample_config.json  # 示例配置文件
│   └── 使用指南.md         # 用户使用指南
│
└── 🎨 资源文件
    └── res/
        ├── logo.ico        # Windows图标文件
        └── logo.png        # 通用PNG图标
```

## 🎯 主要文件说明

### 核心功能模块

**file_filter.py**
- 文件筛选的核心逻辑
- 支持多种筛选条件（扩展名、大小、时间、名称模式）
- 提供文件操作功能（复制、移动、删除）

**config.py**
- 配置文件管理
- 支持保存和加载筛选规则
- JSON格式配置存储

**main.py**
- 命令行界面入口
- 支持命令行参数
- 适合脚本化使用

### 图形界面模块

**gui_pyside6.py** ⭐ 推荐
- 基于Qt6的现代化界面
- 多线程操作，界面不卡顿
- 支持应用程序图标
- 功能最完整，体验最佳

**gui.py**
- 基于tkinter的传统界面
- 兼容性好，无需额外安装
- 功能相对简单

### 启动工具

**启动GUI.py** ⭐ 推荐启动方式
- 智能检测可用的GUI框架
- 自动选择最佳界面版本
- 包含完整的错误处理和帮助信息

**检查依赖.py**
- 全面的依赖检查工具
- 诊断环境问题
- 提供安装建议

### 测试和演示

**demo.py**
- 功能演示程序
- 创建测试文件
- 展示各种筛选功能

**test_file_filter.py**
- 单元测试文件
- 验证核心功能正确性
- 开发和调试使用

### 配置文件

**sample_config.json**
- 包含多种实用的筛选配置示例
- 可直接使用或作为模板修改
- 涵盖常见的文件管理场景

## 🚀 推荐使用方式

### 1. 快速启动（推荐）
```bash
python 启动GUI.py
```
- 自动检测并启动最佳界面
- 包含完整的错误处理

### 2. 直接启动现代界面
```bash
python gui_pyside6.py
```
- 直接启动PySide6界面
- 需要确保PySide6已安装

### 3. 命令行使用
```bash
python main.py --help
```
- 查看命令行参数
- 适合脚本化和自动化

### 4. 依赖检查
```bash
python 检查依赖.py
```
- 检查环境是否正确配置
- 诊断问题并提供解决方案

## 🔧 环境要求

### 基本要求
- Python 3.6+
- 标准库模块（pathlib, json, re等）

### GUI界面要求
- **PySide6界面**：需要安装 `pip install PySide6`
- **tkinter界面**：通常随Python自带

### 可选依赖
- PyYAML：YAML配置文件支持
- pytest：运行单元测试
- tqdm：进度条显示

## 📝 使用建议

1. **首次使用**：运行 `python 检查依赖.py` 检查环境
2. **日常使用**：使用 `python 启动GUI.py` 启动
3. **功能演示**：运行 `python demo.py` 查看示例
4. **配置管理**：参考 `sample_config.json` 创建自定义配置
5. **问题诊断**：查看 `使用指南.md` 获取详细帮助

## 🎨 界面特色

- ✅ 现代化Qt6界面设计
- ✅ 应用程序图标支持
- ✅ 多线程文件操作
- ✅ 实时进度显示
- ✅ 配置保存和加载
- ✅ 完善的错误处理
- ✅ 响应式布局设计

这个项目结构经过优化，删除了无关文件，保留了所有核心功能和实用工具，为用户提供了完整而简洁的文件筛选解决方案。
