"""
UI组件模块
包含各种自定义UI组件
"""

from pathlib import Path
try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
        QLineEdit, QPushButton, QCheckBox, QGroupBox, QSpinBox,
        QTableWidget, QHeaderView, QTextEdit, QFileDialog, QMessageBox,
        QComboBox, QDateEdit
    )
    from PySide6.QtCore import Qt, Signal, QDate
    from PySide6.QtGui import QFont, QPainter, QPen
except ImportError:
    print("PySide6 not available")

class CustomCheckBox(QCheckBox):
    """自定义复选框，支持✓符号显示"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.apply_windows_style()  # 默认使用Windows样式

    def apply_windows_style(self):
        """应用Windows主题样式"""
        self.setStyleSheet("""
            QCheckBox {
                color: #323130;
                spacing: 8px;
                font-weight: normal;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #d1d1d1;
                border-radius: 4px;
                background-color: white;
            }

            QCheckBox::indicator:hover {
                border: 2px solid #0078d4;
                background-color: #deecf9;
            }

            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 2px solid #0078d4;
                color: white;
            }
        """)

    def apply_vs_style(self):
        """应用VS主题样式"""
        self.setStyleSheet("""
            QCheckBox {
                color: #d4d4d4;
                spacing: 8px;
                font-weight: normal;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #5a5a5a;
                border-radius: 4px;
                background-color: #3c3c3c;
            }

            QCheckBox::indicator:hover {
                border: 2px solid #007acc;
                background-color: #2d2d30;
            }

            QCheckBox::indicator:checked {
                background-color: #007acc;
                border: 2px solid #007acc;
                color: white;
            }
        """)

    def paintEvent(self, event):
        """重写绘制事件以显示✓符号"""
        super().paintEvent(event)

        if self.isChecked():
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # 设置画笔 - 白色粗线条
            pen = QPen()
            pen.setColor(Qt.GlobalColor.white)
            pen.setWidth(2)
            pen.setCapStyle(Qt.PenCapStyle.RoundCap)
            painter.setPen(pen)

            # 简化计算：固定位置绘制✓
            # 假设复选框指示器在左侧，大小为18x18
            check_x = 9   # 指示器中心X
            check_y = self.height() // 2  # 指示器中心Y

            # 绘制✓符号 - 简化版本
            # 第一条线：从左下到中间
            painter.drawLine(check_x - 3, check_y, check_x - 1, check_y + 2)
            # 第二条线：从中间到右上
            painter.drawLine(check_x - 1, check_y + 2, check_x + 3, check_y - 2)

class SearchPathWidget(QWidget):
    """搜索路径选择组件"""
    
    path_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择搜索目录...")
        self.path_edit.textChanged.connect(self.path_changed.emit)
        # 设置最小宽度，但允许拉伸
        self.path_edit.setMinimumWidth(200)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_path)
        # 固定按钮宽度
        self.browse_btn.setFixedWidth(80)

        self.recursive_check = CustomCheckBox("递归搜索子目录")
        self.recursive_check.setChecked(True)
        # 设置复选框的最小宽度
        self.recursive_check.setMinimumWidth(120)

        # 添加控件，设置拉伸因子
        layout.addWidget(self.path_edit, 3)  # 路径框占3份
        layout.addWidget(self.browse_btn, 0)  # 按钮不拉伸
        layout.addWidget(self.recursive_check, 1)  # 复选框占1份
    
    def browse_path(self):
        """浏览选择路径"""
        path = QFileDialog.getExistingDirectory(
            self, "选择搜索目录", self.path_edit.text()
        )
        if path:
            self.path_edit.setText(path)
    
    def get_path(self):
        """获取路径"""
        return self.path_edit.text().strip() or "."
    
    def set_path(self, path):
        """设置路径"""
        self.path_edit.setText(path)
    
    def is_recursive(self):
        """是否递归搜索"""
        return self.recursive_check.isChecked()
    
    def set_recursive(self, recursive):
        """设置递归搜索"""
        self.recursive_check.setChecked(recursive)

class FilterConditionsWidget(QWidget):
    """筛选条件组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QGridLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 设置列的拉伸比例
        layout.setColumnStretch(0, 0)  # 标签列不拉伸
        layout.setColumnStretch(1, 1)  # 输入列拉伸

        # 文件扩展名
        label1 = QLabel("文件扩展名:")
        label1.setMinimumWidth(80)
        layout.addWidget(label1, 0, 0)
        self.extensions_edit = QLineEdit()
        self.extensions_edit.setPlaceholderText("如: .txt .py .jpg")
        self.extensions_edit.setMinimumWidth(150)
        layout.addWidget(self.extensions_edit, 0, 1)

        # 文件名模式
        label2 = QLabel("文件名模式:")
        label2.setMinimumWidth(80)
        layout.addWidget(label2, 1, 0)
        self.pattern_edit = QLineEdit()
        self.pattern_edit.setPlaceholderText("支持正则表达式")
        self.pattern_edit.setMinimumWidth(150)
        layout.addWidget(self.pattern_edit, 1, 1)

        # 文件大小
        label3 = QLabel("文件大小:")
        label3.setMinimumWidth(80)
        layout.addWidget(label3, 2, 0)
        size_widget = self.create_size_widget()
        layout.addWidget(size_widget, 2, 1)

        # 修改时间
        label4 = QLabel("修改时间:")
        label4.setMinimumWidth(80)
        layout.addWidget(label4, 3, 0)
        time_widget = self.create_time_widget()
        layout.addWidget(time_widget, 3, 1)
    
    def create_size_widget(self):
        """创建文件大小控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        min_label = QLabel("最小:")
        min_label.setMinimumWidth(35)
        layout.addWidget(min_label)

        self.min_size_edit = QLineEdit()
        self.min_size_edit.setPlaceholderText("如: 1KB")
        self.min_size_edit.setMinimumWidth(70)
        self.min_size_edit.setMaximumWidth(90)
        layout.addWidget(self.min_size_edit)

        max_label = QLabel("最大:")
        max_label.setMinimumWidth(35)
        layout.addWidget(max_label)

        self.max_size_edit = QLineEdit()
        self.max_size_edit.setPlaceholderText("如: 100MB")
        self.max_size_edit.setMinimumWidth(70)
        self.max_size_edit.setMaximumWidth(90)
        layout.addWidget(self.max_size_edit)

        layout.addStretch()  # 添加弹性空间
        return widget
    
    def create_time_widget(self):
        """创建时间控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.days_spin = QSpinBox()
        self.days_spin.setRange(0, 9999)
        self.days_spin.setValue(0)
        self.days_spin.setSpecialValueText("不限制")
        self.days_spin.setMinimumWidth(70)
        self.days_spin.setMaximumWidth(90)
        layout.addWidget(self.days_spin)

        days_label = QLabel("天内修改")
        layout.addWidget(days_label)
        layout.addStretch()  # 添加弹性空间

        return widget
    
    def get_extensions(self):
        """获取扩展名列表"""
        text = self.extensions_edit.text().strip()
        return [ext.strip() for ext in text.split()] if text else None
    
    def get_pattern(self):
        """获取文件名模式"""
        return self.pattern_edit.text().strip() or None
    
    def get_size_range(self):
        """获取文件大小范围"""
        return (
            self.parse_size(self.min_size_edit.text()),
            self.parse_size(self.max_size_edit.text())
        )
    
    def get_days(self):
        """获取修改天数"""
        return self.days_spin.value() if self.days_spin.value() > 0 else None
    
    def parse_size(self, size_str):
        """解析大小字符串"""
        if not size_str.strip():
            return None
        
        size_str = size_str.upper().strip()
        multipliers = {'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
        
        for unit, multiplier in multipliers.items():
            if size_str.endswith(unit):
                try:
                    return int(float(size_str[:-len(unit)]) * multiplier)
                except ValueError:
                    return None
        
        try:
            return int(size_str)
        except ValueError:
            return None
    
    def clear(self):
        """清空所有条件"""
        self.extensions_edit.clear()
        self.pattern_edit.clear()
        self.min_size_edit.clear()
        self.max_size_edit.clear()
        self.days_spin.setValue(0)

class AdvancedFilterWidget(QWidget):
    """高级筛选组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QGridLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 设置列的拉伸比例
        layout.setColumnStretch(0, 0)  # 标签列不拉伸
        layout.setColumnStretch(1, 1)  # 控件列拉伸

        # 重复文件筛选
        dup_label = QLabel("重复文件:")
        dup_label.setMinimumWidth(80)
        layout.addWidget(dup_label, 0, 0)
        duplicate_widget = self.create_duplicate_widget()
        layout.addWidget(duplicate_widget, 0, 1)

        # 文件名相似度筛选
        sim_label = QLabel("文件名相似:")
        sim_label.setMinimumWidth(80)
        layout.addWidget(sim_label, 1, 0)
        similar_widget = self.create_similar_widget()
        layout.addWidget(similar_widget, 1, 1)
    
    def create_duplicate_widget(self):
        """创建重复文件控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.duplicate_check = CustomCheckBox("仅显示重复文件")
        self.duplicate_check.setMinimumWidth(110)
        self.duplicate_check.setMaximumWidth(140)
        layout.addWidget(self.duplicate_check)

        min_dup_label = QLabel("最少重复:")
        min_dup_label.setMinimumWidth(60)
        layout.addWidget(min_dup_label)

        self.min_duplicate_spin = QSpinBox()
        self.min_duplicate_spin.setRange(2, 999)
        self.min_duplicate_spin.setValue(2)
        self.min_duplicate_spin.setSuffix(" 个")
        self.min_duplicate_spin.setMinimumWidth(70)
        self.min_duplicate_spin.setMaximumWidth(80)
        layout.addWidget(self.min_duplicate_spin)

        layout.addStretch()
        return widget
    
    def create_similar_widget(self):
        """创建相似文件控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.similar_check = CustomCheckBox("仅显示相似文件名")
        self.similar_check.setMinimumWidth(120)
        self.similar_check.setMaximumWidth(150)
        layout.addWidget(self.similar_check)

        chars_label = QLabel("相同字符:")
        chars_label.setMinimumWidth(60)
        layout.addWidget(chars_label)

        self.min_common_chars_spin = QSpinBox()
        self.min_common_chars_spin.setRange(3, 50)
        self.min_common_chars_spin.setValue(5)
        self.min_common_chars_spin.setSuffix(" 个")
        self.min_common_chars_spin.setMinimumWidth(65)
        self.min_common_chars_spin.setMaximumWidth(75)
        layout.addWidget(self.min_common_chars_spin)

        sim_label = QLabel("相似度:")
        sim_label.setMinimumWidth(50)
        layout.addWidget(sim_label)

        self.similarity_spin = QSpinBox()
        self.similarity_spin.setRange(50, 100)
        self.similarity_spin.setValue(70)
        self.similarity_spin.setSuffix(" %")
        self.similarity_spin.setMinimumWidth(70)
        self.similarity_spin.setMaximumWidth(80)
        layout.addWidget(self.similarity_spin)

        layout.addStretch()
        return widget
    
    def get_duplicate_settings(self):
        """获取重复文件设置"""
        return (
            self.duplicate_check.isChecked(),
            self.min_duplicate_spin.value()
        )
    
    def get_similar_settings(self):
        """获取相似文件设置"""
        return (
            self.similar_check.isChecked(),
            self.min_common_chars_spin.value(),
            self.similarity_spin.value()
        )
    
    def clear(self):
        """清空设置"""
        self.duplicate_check.setChecked(False)
        self.min_duplicate_spin.setValue(2)
        self.similar_check.setChecked(False)
        self.min_common_chars_spin.setValue(5)
        self.similarity_spin.setValue(70)

class OperationButtonsWidget(QWidget):
    """操作按钮组件"""
    
    search_clicked = Signal()
    copy_clicked = Signal()
    move_clicked = Signal()
    delete_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        self.search_btn = QPushButton("🔍 搜索文件")
        self.copy_btn = QPushButton("📋 复制到...")
        self.move_btn = QPushButton("📁 移动到...")
        self.delete_btn = QPushButton("🗑️ 删除文件")

        # 设置按钮样式 - 更合理的尺寸
        buttons = [self.search_btn, self.copy_btn, self.move_btn, self.delete_btn]
        for btn in buttons:
            btn.setMinimumHeight(35)
            btn.setMaximumHeight(40)
            btn.setMinimumWidth(95)   # 稍微减小最小宽度
            btn.setMaximumWidth(120)  # 减小最大宽度，更紧凑

        # 连接信号
        self.search_btn.clicked.connect(self.search_clicked.emit)
        self.copy_btn.clicked.connect(self.copy_clicked.emit)
        self.move_btn.clicked.connect(self.move_clicked.emit)
        self.delete_btn.clicked.connect(self.delete_clicked.emit)

        # 添加按钮，均匀分布
        for btn in buttons:
            layout.addWidget(btn)

        # 添加弹性空间，让按钮居中
        layout.addStretch()
    
    def set_enabled(self, enabled):
        """设置按钮启用状态"""
        self.search_btn.setEnabled(enabled)
        self.copy_btn.setEnabled(enabled)
        self.move_btn.setEnabled(enabled)
        self.delete_btn.setEnabled(enabled)

class LogWidget(QTextEdit):
    """日志显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setMaximumHeight(120)
        self.setReadOnly(True)
        
    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.append(formatted_message)
        # 自动滚动到底部
        cursor = self.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.setTextCursor(cursor)

class NumberedFileFilterWidget(QWidget):
    """编号文件筛选组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QGridLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 设置列的拉伸比例
        layout.setColumnStretch(0, 0)  # 标签列不拉伸
        layout.setColumnStretch(1, 1)  # 控件列拉伸

        # 启用编号文件筛选
        enable_label = QLabel("编号文件筛选:")
        enable_label.setMinimumWidth(80)
        layout.addWidget(enable_label, 0, 0)

        enable_widget = self.create_enable_widget()
        layout.addWidget(enable_widget, 0, 1)

        # 时间策略
        strategy_label = QLabel("时间策略:")
        strategy_label.setMinimumWidth(80)
        layout.addWidget(strategy_label, 1, 0)

        strategy_widget = self.create_strategy_widget()
        layout.addWidget(strategy_widget, 1, 1)

        # 高级选项
        options_label = QLabel("高级选项:")
        options_label.setMinimumWidth(80)
        layout.addWidget(options_label, 2, 0)

        options_widget = self.create_options_widget()
        layout.addWidget(options_widget, 2, 1)

    def create_enable_widget(self):
        """创建启用控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.enable_check = CustomCheckBox("启用编号文件时间筛选")
        self.enable_check.setMinimumWidth(140)
        self.enable_check.setMaximumWidth(180)
        layout.addWidget(self.enable_check)

        layout.addStretch()
        return widget

    def create_strategy_widget(self):
        """创建策略控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 策略选择
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems([
            "保留最新",
            "保留最旧",
            "保留最新N个",
            "保留最旧N个",
            "保留时间范围",
            "按间隔保留"
        ])
        self.strategy_combo.setMinimumWidth(95)
        self.strategy_combo.setMaximumWidth(115)
        layout.addWidget(self.strategy_combo)

        # 数量输入（用于保留N个）
        count_label = QLabel("数量:")
        count_label.setMinimumWidth(40)
        count_label.setMaximumWidth(45)
        layout.addWidget(count_label)

        self.count_spin = QSpinBox()
        self.count_spin.setRange(1, 100)
        self.count_spin.setValue(1)
        self.count_spin.setMinimumWidth(60)
        self.count_spin.setMaximumWidth(70)
        self.count_spin.setSuffix(" 个")
        layout.addWidget(self.count_spin)

        # 间隔天数（用于按间隔保留）
        interval_label = QLabel("间隔:")
        interval_label.setMinimumWidth(40)
        interval_label.setMaximumWidth(45)
        layout.addWidget(interval_label)

        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 365)
        self.interval_spin.setValue(7)
        self.interval_spin.setMinimumWidth(60)
        self.interval_spin.setMaximumWidth(70)
        self.interval_spin.setSuffix(" 天")
        layout.addWidget(self.interval_spin)

        # 连接信号以动态显示/隐藏控件
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)

        layout.addStretch()
        return widget

    def create_options_widget(self):
        """创建选项控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.ignore_extension_check = CustomCheckBox("忽略扩展名")
        self.ignore_extension_check.setChecked(True)
        self.ignore_extension_check.setMinimumWidth(80)
        self.ignore_extension_check.setMaximumWidth(100)
        layout.addWidget(self.ignore_extension_check)

        self.case_sensitive_check = CustomCheckBox("区分大小写")
        self.case_sensitive_check.setMinimumWidth(80)
        self.case_sensitive_check.setMaximumWidth(100)
        layout.addWidget(self.case_sensitive_check)

        layout.addStretch()
        return widget

    def on_strategy_changed(self, strategy_text):
        """策略改变时的处理"""
        # 根据策略显示/隐藏相关控件
        show_count = strategy_text in ["保留最新N个", "保留最旧N个"]
        show_interval = strategy_text == "按间隔保留"

        # 这里可以添加动态显示/隐藏逻辑
        # 暂时保持所有控件可见，用户可以根据需要设置
        pass

    def get_settings(self):
        """获取筛选设置"""
        if not self.enable_check.isChecked():
            return None

        strategy_map = {
            "保留最新": "keep_newest",
            "保留最旧": "keep_oldest",
            "保留最新N个": "keep_newest_n",
            "保留最旧N个": "keep_oldest_n",
            "保留时间范围": "keep_time_range",
            "按间隔保留": "keep_by_interval"
        }

        strategy = strategy_map.get(self.strategy_combo.currentText(), "keep_newest")

        options = {}
        if strategy in ["keep_newest_n", "keep_oldest_n"]:
            options['count'] = self.count_spin.value()
        elif strategy == "keep_by_interval":
            options['interval_days'] = self.interval_spin.value()

        return {
            'enabled': True,
            'strategy': strategy,
            'options': options,
            'ignore_extension': self.ignore_extension_check.isChecked(),
            'case_sensitive': self.case_sensitive_check.isChecked()
        }

    def clear(self):
        """清空设置"""
        self.enable_check.setChecked(False)
        self.strategy_combo.setCurrentIndex(0)
        self.count_spin.setValue(1)
        self.interval_spin.setValue(7)
        self.ignore_extension_check.setChecked(True)
        self.case_sensitive_check.setChecked(False)
