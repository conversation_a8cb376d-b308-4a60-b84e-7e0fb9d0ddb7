"""
UI组件模块
包含各种自定义UI组件
"""

from pathlib import Path
try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
        QLineEdit, QPushButton, QCheckBox, QGroupBox, QSpinBox,
        QTableWidget, QHeaderView, QTextEdit, QFileDialog, QMessageBox
    )
    from PySide6.QtCore import Qt, Signal
    from PySide6.QtGui import QFont
except ImportError:
    print("PySide6 not available")

class SearchPathWidget(QWidget):
    """搜索路径选择组件"""
    
    path_changed = Signal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("选择搜索目录...")
        self.path_edit.textChanged.connect(self.path_changed.emit)
        # 设置最小宽度，但允许拉伸
        self.path_edit.setMinimumWidth(200)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_path)
        # 固定按钮宽度
        self.browse_btn.setFixedWidth(80)

        self.recursive_check = QCheckBox("递归搜索子目录")
        self.recursive_check.setChecked(True)
        # 设置复选框的最小宽度
        self.recursive_check.setMinimumWidth(120)

        # 添加控件，设置拉伸因子
        layout.addWidget(self.path_edit, 3)  # 路径框占3份
        layout.addWidget(self.browse_btn, 0)  # 按钮不拉伸
        layout.addWidget(self.recursive_check, 1)  # 复选框占1份
    
    def browse_path(self):
        """浏览选择路径"""
        path = QFileDialog.getExistingDirectory(
            self, "选择搜索目录", self.path_edit.text()
        )
        if path:
            self.path_edit.setText(path)
    
    def get_path(self):
        """获取路径"""
        return self.path_edit.text().strip() or "."
    
    def set_path(self, path):
        """设置路径"""
        self.path_edit.setText(path)
    
    def is_recursive(self):
        """是否递归搜索"""
        return self.recursive_check.isChecked()
    
    def set_recursive(self, recursive):
        """设置递归搜索"""
        self.recursive_check.setChecked(recursive)

class FilterConditionsWidget(QWidget):
    """筛选条件组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QGridLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 设置列的拉伸比例
        layout.setColumnStretch(0, 0)  # 标签列不拉伸
        layout.setColumnStretch(1, 1)  # 输入列拉伸

        # 文件扩展名
        label1 = QLabel("文件扩展名:")
        label1.setMinimumWidth(80)
        layout.addWidget(label1, 0, 0)
        self.extensions_edit = QLineEdit()
        self.extensions_edit.setPlaceholderText("如: .txt .py .jpg")
        self.extensions_edit.setMinimumWidth(150)
        layout.addWidget(self.extensions_edit, 0, 1)

        # 文件名模式
        label2 = QLabel("文件名模式:")
        label2.setMinimumWidth(80)
        layout.addWidget(label2, 1, 0)
        self.pattern_edit = QLineEdit()
        self.pattern_edit.setPlaceholderText("支持正则表达式")
        self.pattern_edit.setMinimumWidth(150)
        layout.addWidget(self.pattern_edit, 1, 1)

        # 文件大小
        label3 = QLabel("文件大小:")
        label3.setMinimumWidth(80)
        layout.addWidget(label3, 2, 0)
        size_widget = self.create_size_widget()
        layout.addWidget(size_widget, 2, 1)

        # 修改时间
        label4 = QLabel("修改时间:")
        label4.setMinimumWidth(80)
        layout.addWidget(label4, 3, 0)
        time_widget = self.create_time_widget()
        layout.addWidget(time_widget, 3, 1)
    
    def create_size_widget(self):
        """创建文件大小控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        min_label = QLabel("最小:")
        min_label.setMinimumWidth(35)
        layout.addWidget(min_label)

        self.min_size_edit = QLineEdit()
        self.min_size_edit.setPlaceholderText("如: 1KB")
        self.min_size_edit.setMaximumWidth(100)
        layout.addWidget(self.min_size_edit)

        max_label = QLabel("最大:")
        max_label.setMinimumWidth(35)
        layout.addWidget(max_label)

        self.max_size_edit = QLineEdit()
        self.max_size_edit.setPlaceholderText("如: 100MB")
        self.max_size_edit.setMaximumWidth(100)
        layout.addWidget(self.max_size_edit)

        layout.addStretch()  # 添加弹性空间
        return widget
    
    def create_time_widget(self):
        """创建时间控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.days_spin = QSpinBox()
        self.days_spin.setRange(0, 9999)
        self.days_spin.setValue(0)
        self.days_spin.setSpecialValueText("不限制")
        self.days_spin.setMaximumWidth(100)
        layout.addWidget(self.days_spin)

        days_label = QLabel("天内修改")
        layout.addWidget(days_label)
        layout.addStretch()  # 添加弹性空间

        return widget
    
    def get_extensions(self):
        """获取扩展名列表"""
        text = self.extensions_edit.text().strip()
        return [ext.strip() for ext in text.split()] if text else None
    
    def get_pattern(self):
        """获取文件名模式"""
        return self.pattern_edit.text().strip() or None
    
    def get_size_range(self):
        """获取文件大小范围"""
        return (
            self.parse_size(self.min_size_edit.text()),
            self.parse_size(self.max_size_edit.text())
        )
    
    def get_days(self):
        """获取修改天数"""
        return self.days_spin.value() if self.days_spin.value() > 0 else None
    
    def parse_size(self, size_str):
        """解析大小字符串"""
        if not size_str.strip():
            return None
        
        size_str = size_str.upper().strip()
        multipliers = {'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
        
        for unit, multiplier in multipliers.items():
            if size_str.endswith(unit):
                try:
                    return int(float(size_str[:-len(unit)]) * multiplier)
                except ValueError:
                    return None
        
        try:
            return int(size_str)
        except ValueError:
            return None
    
    def clear(self):
        """清空所有条件"""
        self.extensions_edit.clear()
        self.pattern_edit.clear()
        self.min_size_edit.clear()
        self.max_size_edit.clear()
        self.days_spin.setValue(0)

class AdvancedFilterWidget(QWidget):
    """高级筛选组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QGridLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 设置列的拉伸比例
        layout.setColumnStretch(0, 0)  # 标签列不拉伸
        layout.setColumnStretch(1, 1)  # 控件列拉伸

        # 重复文件筛选
        dup_label = QLabel("重复文件:")
        dup_label.setMinimumWidth(80)
        layout.addWidget(dup_label, 0, 0)
        duplicate_widget = self.create_duplicate_widget()
        layout.addWidget(duplicate_widget, 0, 1)

        # 文件名相似度筛选
        sim_label = QLabel("文件名相似:")
        sim_label.setMinimumWidth(80)
        layout.addWidget(sim_label, 1, 0)
        similar_widget = self.create_similar_widget()
        layout.addWidget(similar_widget, 1, 1)
    
    def create_duplicate_widget(self):
        """创建重复文件控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.duplicate_check = QCheckBox("仅显示重复文件")
        self.duplicate_check.setMinimumWidth(120)
        layout.addWidget(self.duplicate_check)

        min_dup_label = QLabel("最少重复:")
        layout.addWidget(min_dup_label)

        self.min_duplicate_spin = QSpinBox()
        self.min_duplicate_spin.setRange(2, 999)
        self.min_duplicate_spin.setValue(2)
        self.min_duplicate_spin.setSuffix(" 个")
        self.min_duplicate_spin.setMaximumWidth(80)
        layout.addWidget(self.min_duplicate_spin)

        layout.addStretch()
        return widget
    
    def create_similar_widget(self):
        """创建相似文件控件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        self.similar_check = QCheckBox("仅显示相似文件名")
        self.similar_check.setMinimumWidth(130)
        layout.addWidget(self.similar_check)

        chars_label = QLabel("相同字符:")
        layout.addWidget(chars_label)

        self.min_common_chars_spin = QSpinBox()
        self.min_common_chars_spin.setRange(3, 50)
        self.min_common_chars_spin.setValue(5)
        self.min_common_chars_spin.setSuffix(" 个")
        self.min_common_chars_spin.setMaximumWidth(70)
        layout.addWidget(self.min_common_chars_spin)

        sim_label = QLabel("相似度:")
        layout.addWidget(sim_label)

        self.similarity_spin = QSpinBox()
        self.similarity_spin.setRange(50, 100)
        self.similarity_spin.setValue(70)
        self.similarity_spin.setSuffix(" %")
        self.similarity_spin.setMaximumWidth(80)
        layout.addWidget(self.similarity_spin)

        layout.addStretch()
        return widget
    
    def get_duplicate_settings(self):
        """获取重复文件设置"""
        return (
            self.duplicate_check.isChecked(),
            self.min_duplicate_spin.value()
        )
    
    def get_similar_settings(self):
        """获取相似文件设置"""
        return (
            self.similar_check.isChecked(),
            self.min_common_chars_spin.value(),
            self.similarity_spin.value()
        )
    
    def clear(self):
        """清空设置"""
        self.duplicate_check.setChecked(False)
        self.min_duplicate_spin.setValue(2)
        self.similar_check.setChecked(False)
        self.min_common_chars_spin.setValue(5)
        self.similarity_spin.setValue(70)

class OperationButtonsWidget(QWidget):
    """操作按钮组件"""
    
    search_clicked = Signal()
    copy_clicked = Signal()
    move_clicked = Signal()
    delete_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        self.search_btn = QPushButton("🔍 搜索文件")
        self.copy_btn = QPushButton("📋 复制到...")
        self.move_btn = QPushButton("📁 移动到...")
        self.delete_btn = QPushButton("🗑️ 删除文件")

        # 设置按钮样式 - 更合理的尺寸
        buttons = [self.search_btn, self.copy_btn, self.move_btn, self.delete_btn]
        for btn in buttons:
            btn.setMinimumHeight(35)
            btn.setMaximumHeight(40)
            btn.setMinimumWidth(100)
            btn.setMaximumWidth(130)  # 稍微增加最大宽度

        # 连接信号
        self.search_btn.clicked.connect(self.search_clicked.emit)
        self.copy_btn.clicked.connect(self.copy_clicked.emit)
        self.move_btn.clicked.connect(self.move_clicked.emit)
        self.delete_btn.clicked.connect(self.delete_clicked.emit)

        # 添加按钮，均匀分布
        for btn in buttons:
            layout.addWidget(btn)

        # 添加弹性空间，让按钮居中
        layout.addStretch()
    
    def set_enabled(self, enabled):
        """设置按钮启用状态"""
        self.search_btn.setEnabled(enabled)
        self.copy_btn.setEnabled(enabled)
        self.move_btn.setEnabled(enabled)
        self.delete_btn.setEnabled(enabled)

class LogWidget(QTextEdit):
    """日志显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setMaximumHeight(120)
        self.setReadOnly(True)
        
    def log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.append(formatted_message)
        # 自动滚动到底部
        cursor = self.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.setTextCursor(cursor)
