#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版重复文件检测功能测试
测试健壮性和完整性
"""

import sys
import tempfile
import hashlib
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QTextEdit, QProgressBar
    )
    from file_filter_tool.core.duplicate_detector import DuplicateDetector
    from file_filter_tool.file_filter import FileFilter
    from file_filter_tool.ui.duplicate_stats_dialog import DuplicateStatsDialog
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class EnhancedDuplicateTestWidget(QWidget):
    """增强版重复文件检测测试控件"""
    
    def __init__(self):
        super().__init__()
        self.temp_dir = None
        self.test_files = []
        self.duplicate_detector = DuplicateDetector()
        self.setup_ui()
        self.create_test_files()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("增强版重复文件检测功能测试")
        self.setMinimumSize(1000, 800)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🔍 增强版重复文件检测功能测试

本测试验证以下增强功能：

✅ 健壮性改进：
• 多线程并行哈希计算
• 内存映射处理大文件
• 快速哈希预筛选
• 完善的错误处理
• 进度报告和取消功能

✅ 统计功能：
• 详细的统计信息
• 文件类型分布
• 大小分布分析
• 智能清理建议
• 空间节省计算

✅ 验证功能：
• 抽样验证检测结果
• 哈希值一致性检查
• 错误率统计

✅ 导出功能：
• CSV格式报告
• JSON格式报告
• 文本格式报告
• 完整统计信息

🎯 测试场景：
• 创建各种类型的测试文件
• 模拟真实的重复文件场景
• 测试大文件处理能力
• 验证统计准确性
        """)
        layout.addWidget(info_label)
        
        # 测试控制区域
        control_group = QGroupBox("测试控制")
        control_layout = QHBoxLayout(control_group)
        
        # 创建测试文件按钮
        create_btn = QPushButton("创建测试文件")
        create_btn.clicked.connect(self.create_test_files)
        control_layout.addWidget(create_btn)
        
        # 基础检测按钮
        basic_btn = QPushButton("基础重复检测")
        basic_btn.clicked.connect(self.test_basic_detection)
        control_layout.addWidget(basic_btn)
        
        # 增强检测按钮
        enhanced_btn = QPushButton("增强重复检测")
        enhanced_btn.clicked.connect(self.test_enhanced_detection)
        control_layout.addWidget(enhanced_btn)
        
        # 统计分析按钮
        stats_btn = QPushButton("统计分析")
        stats_btn.clicked.connect(self.test_statistics)
        control_layout.addWidget(stats_btn)
        
        # 验证结果按钮
        verify_btn = QPushButton("验证结果")
        verify_btn.clicked.connect(self.test_verification)
        control_layout.addWidget(verify_btn)
        
        # 导出测试按钮
        export_btn = QPushButton("导出测试")
        export_btn.clicked.connect(self.test_export)
        control_layout.addWidget(export_btn)
        
        layout.addWidget(control_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 结果显示区域
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMinimumHeight(400)
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
        
        # 清理按钮
        cleanup_layout = QHBoxLayout()
        cleanup_layout.addStretch()
        
        cleanup_btn = QPushButton("清理测试文件")
        cleanup_btn.clicked.connect(self.cleanup_test_files)
        cleanup_layout.addWidget(cleanup_btn)
        
        layout.addLayout(cleanup_layout)
    
    def create_test_files(self):
        """创建测试文件"""
        try:
            # 清理旧的测试文件
            self.cleanup_test_files()
            
            # 创建临时目录
            self.temp_dir = Path(tempfile.mkdtemp(prefix="duplicate_test_"))
            self.log(f"创建测试目录: {self.temp_dir}")
            
            # 创建不同类型的测试文件
            test_scenarios = [
                # 完全相同的文件
                ("identical_1.txt", "这是一个测试文件的内容"),
                ("identical_2.txt", "这是一个测试文件的内容"),
                ("identical_3.txt", "这是一个测试文件的内容"),
                
                # 不同扩展名但内容相同
                ("same_content.doc", "相同内容的文档"),
                ("same_content.txt", "相同内容的文档"),
                
                # 大小相同但内容不同
                ("different_1.txt", "内容A" + "x" * 100),
                ("different_2.txt", "内容B" + "y" * 100),
                
                # 大文件测试
                ("large_1.bin", "大文件内容" * 10000),
                ("large_2.bin", "大文件内容" * 10000),
                
                # 空文件
                ("empty_1.txt", ""),
                ("empty_2.txt", ""),
                
                # 单字节文件
                ("single_1.txt", "A"),
                ("single_2.txt", "A"),
                
                # 图片文件模拟
                ("image_1.jpg", b"\xFF\xD8\xFF\xE0" + b"fake_jpeg_data" * 100),
                ("image_2.jpg", b"\xFF\xD8\xFF\xE0" + b"fake_jpeg_data" * 100),
                
                # 文档文件模拟
                ("doc_1.pdf", b"%PDF-1.4" + b"fake_pdf_content" * 50),
                ("doc_2.pdf", b"%PDF-1.4" + b"fake_pdf_content" * 50),
            ]
            
            self.test_files = []
            for filename, content in test_scenarios:
                file_path = self.temp_dir / filename
                
                if isinstance(content, str):
                    file_path.write_text(content, encoding='utf-8')
                else:
                    file_path.write_bytes(content)
                
                self.test_files.append(file_path)
            
            self.log(f"创建了 {len(self.test_files)} 个测试文件")
            self.log("测试文件创建完成！")
            
        except Exception as e:
            self.log(f"创建测试文件失败: {e}")
    
    def test_basic_detection(self):
        """测试基础重复检测"""
        if not self.test_files:
            self.log("请先创建测试文件")
            return
        
        try:
            self.log("\n=== 基础重复文件检测测试 ===")
            
            # 使用基础检测器
            file_filter = FileFilter(str(self.temp_dir))
            
            # 查找重复文件
            duplicate_files = file_filter._find_duplicate_files(self.test_files, min_count=2)
            
            self.log(f"基础检测发现 {len(duplicate_files)} 个重复文件")
            
            # 获取分组信息
            duplicate_groups = file_filter.get_duplicate_groups(duplicate_files)
            self.log(f"分为 {len(duplicate_groups)} 个重复组")
            
            # 显示分组详情
            for i, (hash_val, files) in enumerate(duplicate_groups.items(), 1):
                self.log(f"组 {i} (哈希: {hash_val[:8]}...): {len(files)} 个文件")
                for file_path in files:
                    self.log(f"  - {file_path.name}")
            
        except Exception as e:
            self.log(f"基础检测测试失败: {e}")
    
    def test_enhanced_detection(self):
        """测试增强重复检测"""
        if not self.test_files:
            self.log("请先创建测试文件")
            return
        
        try:
            self.log("\n=== 增强重复文件检测测试 ===")
            
            # 设置进度回调
            self.duplicate_detector.set_progress_callback(self.on_progress)
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            
            # 执行增强检测
            duplicate_groups = self.duplicate_detector.detect_duplicates(
                self.test_files, 
                min_duplicate_count=2,
                use_quick_hash=True
            )
            
            self.log(f"增强检测发现 {len(duplicate_groups)} 个重复组")
            
            # 显示详细结果
            total_duplicates = sum(len(files) for files in duplicate_groups.values())
            self.log(f"总重复文件数: {total_duplicates}")
            
            # 隐藏进度条
            self.progress_bar.setVisible(False)
            
        except Exception as e:
            self.log(f"增强检测测试失败: {e}")
            self.progress_bar.setVisible(False)
    
    def test_statistics(self):
        """测试统计功能"""
        if not self.test_files:
            self.log("请先创建测试文件")
            return
        
        try:
            self.log("\n=== 统计功能测试 ===")
            
            # 执行检测
            duplicate_groups = self.duplicate_detector.detect_duplicates(self.test_files)
            
            if not duplicate_groups:
                self.log("未发现重复文件，无法测试统计功能")
                return
            
            # 获取统计信息
            stats = self.duplicate_detector.get_duplicate_statistics(duplicate_groups)
            
            # 显示统计结果
            self.log("统计信息:")
            self.log(f"  重复文件总数: {stats['total_files']}")
            self.log(f"  重复组数: {stats['total_groups']}")
            self.log(f"  总大小: {self._format_size(stats['total_size'])}")
            self.log(f"  浪费空间: {self._format_size(stats['wasted_size'])}")
            self.log(f"  空间节省率: {stats['space_saving_ratio']:.1f}%")
            self.log(f"  最大重复组: {stats['largest_group_size']} 个文件")
            
            # 文件类型分布
            self.log("\n文件类型分布:")
            for file_type, count in stats['file_type_distribution'].items():
                self.log(f"  {file_type}: {count} 个")
            
            # 大小分布
            self.log("\n大小分布:")
            size_dist = stats['size_distribution']
            self.log(f"  微小文件: {size_dist['tiny']}")
            self.log(f"  小文件: {size_dist['small']}")
            self.log(f"  中等文件: {size_dist['medium']}")
            self.log(f"  大文件: {size_dist['large']}")
            self.log(f"  超大文件: {size_dist['huge']}")
            
            # 清理建议
            self.log("\n清理建议:")
            for i, rec in enumerate(stats['recommendations'], 1):
                self.log(f"  {i}. {rec}")
            
        except Exception as e:
            self.log(f"统计功能测试失败: {e}")
    
    def test_verification(self):
        """测试验证功能"""
        if not self.test_files:
            self.log("请先创建测试文件")
            return
        
        try:
            self.log("\n=== 验证功能测试 ===")
            
            # 执行检测
            duplicate_groups = self.duplicate_detector.detect_duplicates(self.test_files)
            
            if not duplicate_groups:
                self.log("未发现重复文件，无法测试验证功能")
                return
            
            # 执行验证
            verification = self.duplicate_detector.verify_duplicates(
                duplicate_groups, sample_ratio=0.5
            )
            
            # 显示验证结果
            self.log("验证结果:")
            self.log(f"  验证通过: {verification['verified']}")
            self.log(f"  抽样数量: {verification['sample_count']}")
            self.log(f"  总组数: {verification['total_groups']}")
            self.log(f"  错误数量: {len(verification['errors'])}")
            self.log(f"  错误率: {verification['error_rate']:.1%}")
            
            if verification['errors']:
                self.log("\n错误详情:")
                for error in verification['errors'][:3]:  # 只显示前3个错误
                    self.log(f"  - {error}")
            
        except Exception as e:
            self.log(f"验证功能测试失败: {e}")
    
    def test_export(self):
        """测试导出功能"""
        if not self.test_files:
            self.log("请先创建测试文件")
            return
        
        try:
            self.log("\n=== 导出功能测试 ===")
            
            # 执行检测
            duplicate_groups = self.duplicate_detector.detect_duplicates(self.test_files)
            
            if not duplicate_groups:
                self.log("未发现重复文件，无法测试导出功能")
                return
            
            # 测试不同格式的导出
            formats = ['csv', 'json', 'txt']
            
            for format_type in formats:
                output_path = self.temp_dir / f"duplicate_report.{format_type}"
                
                success = self.duplicate_detector.export_duplicate_report(
                    duplicate_groups, output_path, format_type
                )
                
                if success and output_path.exists():
                    file_size = output_path.stat().st_size
                    self.log(f"  {format_type.upper()} 导出成功: {output_path.name} ({file_size} 字节)")
                else:
                    self.log(f"  {format_type.upper()} 导出失败")
            
        except Exception as e:
            self.log(f"导出功能测试失败: {e}")
    
    def on_progress(self, current: int, total: int, message: str):
        """进度回调"""
        self.progress_bar.setValue(current)
        self.log(f"进度: {message} ({current}/{total})")
    
    def log(self, message: str):
        """记录日志"""
        self.result_text.append(message)
        self.result_text.ensureCursorVisible()
        QApplication.processEvents()  # 更新UI
    
    def cleanup_test_files(self):
        """清理测试文件"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                self.log(f"清理测试目录: {self.temp_dir}")
                self.temp_dir = None
                self.test_files = []
            except Exception as e:
                self.log(f"清理失败: {e}")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def closeEvent(self, event):
        """关闭事件"""
        self.cleanup_test_files()
        event.accept()

def test_enhanced_duplicate_detection():
    """测试增强版重复文件检测"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行增强版重复检测测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🔍 增强版重复文件检测功能测试启动")
    print("=" * 60)
    print("✅ 测试功能:")
    print("  • 健壮性改进：多线程、内存映射、错误处理")
    print("  • 统计功能：详细统计、类型分布、清理建议")
    print("  • 验证功能：抽样验证、一致性检查")
    print("  • 导出功能：多格式报告导出")
    print()
    print("🎯 测试场景:")
    print("  • 各种类型的重复文件")
    print("  • 大文件处理能力")
    print("  • 边界情况处理")
    print("  • 统计准确性验证")
    print()
    
    # 创建测试窗口
    test_widget = EnhancedDuplicateTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行增强版重复检测测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_enhanced_duplicate_detection()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
