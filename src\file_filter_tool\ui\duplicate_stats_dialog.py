#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复文件统计对话框
显示详细的重复文件统计信息和建议
"""

try:
    from PySide6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, 
        QPushButton, QTabWidget, QWidget, QLabel, QTableWidget,
        QTableWidgetItem, QHeaderView, QGroupBox, QProgressBar,
        QFileDialog, QMessageBox, QSplitter
    )
    from PySide6.QtCore import Qt, QThread, Signal
    from PySide6.QtGui import QFont
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

from pathlib import Path
import time
from typing import Dict, List, Any


class DuplicateStatsDialog(QDialog):
    """重复文件统计对话框"""
    
    def __init__(self, stats: Dict[str, Any], duplicate_groups: Dict[str, List[Path]], parent=None):
        super().__init__(parent)
        self.stats = stats
        self.duplicate_groups = duplicate_groups
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("重复文件统计报告")
        self.setMinimumSize(900, 700)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 统计概览标签页
        overview_tab = self.create_overview_tab()
        tab_widget.addTab(overview_tab, "统计概览")
        
        # 详细列表标签页
        details_tab = self.create_details_tab()
        tab_widget.addTab(details_tab, "详细列表")
        
        # 文件类型分布标签页
        distribution_tab = self.create_distribution_tab()
        tab_widget.addTab(distribution_tab, "类型分布")
        
        # 清理建议标签页
        recommendations_tab = self.create_recommendations_tab()
        tab_widget.addTab(recommendations_tab, "清理建议")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 导出报告按钮
        export_btn = QPushButton("导出报告")
        export_btn.clicked.connect(self.export_report)
        button_layout.addWidget(export_btn)
        
        # 验证结果按钮
        verify_btn = QPushButton("验证结果")
        verify_btn.clicked.connect(self.verify_results)
        button_layout.addWidget(verify_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        close_btn.setMinimumWidth(80)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def create_overview_tab(self):
        """创建统计概览标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主要统计信息
        stats_group = QGroupBox("统计概览")
        stats_layout = QVBoxLayout(stats_group)
        
        stats_text = QTextEdit()
        stats_text.setReadOnly(True)
        stats_text.setMaximumHeight(200)
        
        # 格式化统计信息
        overview_html = f"""
        <h3>🔍 重复文件检测结果</h3>
        <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%;">
            <tr><td><b>重复文件总数</b></td><td>{self.stats['total_files']} 个</td></tr>
            <tr><td><b>重复组数</b></td><td>{self.stats['total_groups']} 组</td></tr>
            <tr><td><b>总占用空间</b></td><td>{self._format_size(self.stats['total_size'])}</td></tr>
            <tr><td><b>浪费空间</b></td><td>{self._format_size(self.stats['wasted_size'])}</td></tr>
            <tr><td><b>空间节省率</b></td><td>{self.stats['space_saving_ratio']:.1f}%</td></tr>
            <tr><td><b>最大重复组</b></td><td>{self.stats['largest_group_size']} 个文件</td></tr>
            <tr><td><b>最大文件</b></td><td>{self._format_size(self.stats['largest_file_size'])}</td></tr>
        </table>
        """
        
        stats_text.setHtml(overview_html)
        stats_layout.addWidget(stats_text)
        layout.addWidget(stats_group)
        
        # 大小分布图表
        size_group = QGroupBox("文件大小分布")
        size_layout = QVBoxLayout(size_group)
        
        size_text = QTextEdit()
        size_text.setReadOnly(True)
        size_text.setMaximumHeight(150)
        
        size_dist = self.stats['size_distribution']
        size_html = f"""
        <h4>📊 按文件大小分布</h4>
        <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
            <tr><th>大小范围</th><th>文件数量</th><th>占比</th></tr>
            <tr><td>微小文件 (&lt; 1KB)</td><td>{size_dist['tiny']}</td><td>{size_dist['tiny']/self.stats['total_files']*100:.1f}%</td></tr>
            <tr><td>小文件 (1KB - 1MB)</td><td>{size_dist['small']}</td><td>{size_dist['small']/self.stats['total_files']*100:.1f}%</td></tr>
            <tr><td>中等文件 (1MB - 100MB)</td><td>{size_dist['medium']}</td><td>{size_dist['medium']/self.stats['total_files']*100:.1f}%</td></tr>
            <tr><td>大文件 (100MB - 1GB)</td><td>{size_dist['large']}</td><td>{size_dist['large']/self.stats['total_files']*100:.1f}%</td></tr>
            <tr><td>超大文件 (&gt; 1GB)</td><td>{size_dist['huge']}</td><td>{size_dist['huge']/self.stats['total_files']*100:.1f}%</td></tr>
        </table>
        """
        
        size_text.setHtml(size_html)
        size_layout.addWidget(size_text)
        layout.addWidget(size_group)
        
        return widget
    
    def create_details_tab(self):
        """创建详细列表标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明标签
        info_label = QLabel("以下是所有重复文件的详细列表，按组显示：")
        layout.addWidget(info_label)
        
        # 创建表格
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(5)
        self.details_table.setHorizontalHeaderLabels([
            "组ID", "文件路径", "文件大小", "修改时间", "建议操作"
        ])
        
        # 填充表格数据
        self.populate_details_table()
        
        # 设置表格属性
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 文件路径列自适应
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 组ID列
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 大小列
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 时间列
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 建议列
        
        self.details_table.setAlternatingRowColors(True)
        self.details_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.details_table)
        
        return widget
    
    def create_distribution_tab(self):
        """创建文件类型分布标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 文件类型统计
        type_group = QGroupBox("文件类型分布")
        type_layout = QVBoxLayout(type_group)
        
        type_text = QTextEdit()
        type_text.setReadOnly(True)
        
        # 生成文件类型分布HTML
        type_dist = self.stats['file_type_distribution']
        if type_dist:
            type_html = "<h3>📁 文件类型分布</h3>"
            type_html += "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>"
            type_html += "<tr><th>文件类型</th><th>重复文件数量</th><th>占比</th></tr>"
            
            # 按数量排序
            sorted_types = sorted(type_dist.items(), key=lambda x: x[1], reverse=True)
            
            for file_type, count in sorted_types:
                percentage = count / self.stats['total_files'] * 100
                type_html += f"<tr><td>{file_type}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>"
            
            type_html += "</table>"
        else:
            type_html = "<p>暂无文件类型分布数据</p>"
        
        type_text.setHtml(type_html)
        type_layout.addWidget(type_text)
        layout.addWidget(type_group)
        
        return widget
    
    def create_recommendations_tab(self):
        """创建清理建议标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 清理建议
        rec_group = QGroupBox("智能清理建议")
        rec_layout = QVBoxLayout(rec_group)
        
        rec_text = QTextEdit()
        rec_text.setReadOnly(True)
        
        recommendations = self.stats.get('recommendations', [])
        if recommendations:
            rec_html = "<h3>💡 清理建议</h3><ul>"
            for i, rec in enumerate(recommendations, 1):
                rec_html += f"<li><b>建议 {i}:</b> {rec}</li>"
            rec_html += "</ul>"
            
            # 添加通用建议
            rec_html += """
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li><b>备份重要文件:</b> 在删除任何文件之前，请确保已备份重要数据</li>
                <li><b>仔细检查:</b> 相同哈希值不一定意味着文件完全相同，建议手动检查</li>
                <li><b>保留最新版本:</b> 通常建议保留修改时间最新的文件</li>
                <li><b>分批处理:</b> 对于大量文件，建议分批处理以避免误操作</li>
                <li><b>验证结果:</b> 可以使用"验证结果"功能检查检测准确性</li>
            </ul>
            
            <h3>🔧 操作建议</h3>
            <ol>
                <li>首先处理大文件，可以快速释放大量空间</li>
                <li>检查重复的媒体文件（图片、视频），这些通常是真正的重复</li>
                <li>对于文档文件，建议手动检查内容差异</li>
                <li>使用"导出报告"功能保存检测结果</li>
            </ol>
            """
        else:
            rec_html = "<p>暂无清理建议</p>"
        
        rec_text.setHtml(rec_html)
        rec_layout.addWidget(rec_text)
        layout.addWidget(rec_group)
        
        return widget
    
    def populate_details_table(self):
        """填充详细列表表格"""
        total_rows = sum(len(files) for files in self.duplicate_groups.values())
        self.details_table.setRowCount(total_rows)
        
        row = 0
        for group_id, (hash_value, file_list) in enumerate(self.duplicate_groups.items(), 1):
            for i, file_path in enumerate(file_list):
                try:
                    stat = file_path.stat()
                    size_str = self._format_size(stat.st_size)
                    time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))
                    
                    # 建议操作
                    if i == 0:  # 第一个文件（最新）
                        suggestion = "保留（最新）"
                    else:
                        suggestion = "可删除"
                    
                except OSError:
                    size_str = "N/A"
                    time_str = "N/A"
                    suggestion = "检查文件"
                
                # 填充表格
                self.details_table.setItem(row, 0, QTableWidgetItem(str(group_id)))
                self.details_table.setItem(row, 1, QTableWidgetItem(str(file_path)))
                self.details_table.setItem(row, 2, QTableWidgetItem(size_str))
                self.details_table.setItem(row, 3, QTableWidgetItem(time_str))
                self.details_table.setItem(row, 4, QTableWidgetItem(suggestion))
                
                row += 1
    
    def export_report(self):
        """导出报告"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出重复文件报告", 
            f"重复文件报告_{time.strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV文件 (*.csv);;JSON文件 (*.json);;文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                # 确定文件格式
                if file_path.endswith('.json'):
                    format_type = 'json'
                elif file_path.endswith('.txt'):
                    format_type = 'txt'
                else:
                    format_type = 'csv'
                
                # 导出报告（这里需要访问父窗口的file_filter）
                if hasattr(self.parent(), 'file_filter'):
                    success = self.parent().file_filter.export_duplicate_report(
                        self.duplicate_groups, Path(file_path), format_type
                    )
                    
                    if success:
                        QMessageBox.information(self, "成功", f"报告已导出到: {file_path}")
                    else:
                        QMessageBox.warning(self, "失败", "报告导出失败")
                else:
                    QMessageBox.warning(self, "错误", "无法访问文件筛选器")
                    
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {e}")
    
    def verify_results(self):
        """验证检测结果"""
        try:
            if hasattr(self.parent(), 'file_filter'):
                # 显示进度
                progress = QProgressBar()
                progress.setRange(0, 0)  # 不确定进度
                
                # 执行验证
                verification = self.parent().file_filter.verify_duplicates(
                    self.duplicate_groups, sample_ratio=0.1
                )
                
                # 显示验证结果
                if verification['verified']:
                    QMessageBox.information(
                        self, "验证结果", 
                        f"验证通过！\n抽样检查了 {verification['sample_count']} 组文件，未发现错误。"
                    )
                else:
                    error_msg = f"验证发现问题！\n"
                    error_msg += f"抽样检查: {verification['sample_count']} 组\n"
                    error_msg += f"错误数量: {len(verification['errors'])}\n"
                    error_msg += f"错误率: {verification['error_rate']:.1%}\n\n"
                    error_msg += "前几个错误:\n" + "\n".join(verification['errors'][:3])
                    
                    QMessageBox.warning(self, "验证结果", error_msg)
            else:
                QMessageBox.warning(self, "错误", "无法访问文件筛选器")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"验证失败: {e}")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
