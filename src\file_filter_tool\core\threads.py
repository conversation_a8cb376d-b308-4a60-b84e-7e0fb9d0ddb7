"""
线程管理模块
包含各种后台任务线程
"""

from pathlib import Path
try:
    from PySide6.QtCore import QThread, Signal
except ImportError:
    print("PySide6 not available")

class FileSearchThread(QThread):
    """文件搜索线程"""
    
    finished = Signal(list)  # 搜索完成信号
    progress = Signal(str)   # 进度信号
    error = Signal(str)      # 错误信号
    
    def __init__(self, file_filter, **kwargs):
        super().__init__()
        self.file_filter = file_filter
        self.search_params = kwargs
    
    def run(self):
        """执行搜索"""
        try:
            self.progress.emit("开始搜索文件...")
            files = self.file_filter.find_files(**self.search_params)
            self.progress.emit(f"搜索完成，找到 {len(files)} 个文件")
            self.finished.emit(files)
        except Exception as e:
            self.error.emit(str(e))

class FileOperationThread(QThread):
    """文件操作线程"""
    
    finished = Signal(str)   # 操作完成信号
    progress = Signal(str)   # 进度信号
    error = Signal(str)      # 错误信号
    
    def __init__(self, file_filter, operation, files, destination=None):
        super().__init__()
        self.file_filter = file_filter
        self.operation = operation
        self.files = files
        self.destination = destination
    
    def run(self):
        """执行文件操作"""
        try:
            total = len(self.files)
            operation_names = {
                "copy": "复制",
                "move": "移动", 
                "delete": "删除"
            }
            op_name = operation_names.get(self.operation, self.operation)
            
            self.progress.emit(f"开始{op_name} {total} 个文件...")
            
            success_count = 0
            for i, file_path in enumerate(self.files, 1):
                try:
                    if self.operation == "copy":
                        self.file_filter.copy_file(file_path, self.destination)
                    elif self.operation == "move":
                        self.file_filter.move_file(file_path, self.destination)
                    elif self.operation == "delete":
                        self.file_filter.delete_file(file_path)
                    
                    success_count += 1
                    
                    # 每处理10个文件报告一次进度
                    if i % 10 == 0 or i == total:
                        self.progress.emit(f"已{op_name} {i}/{total} 个文件")
                        
                except Exception as e:
                    self.progress.emit(f"处理文件失败: {file_path.name} - {e}")
            
            if success_count == total:
                self.finished.emit(f"成功{op_name} {success_count} 个文件")
            else:
                self.finished.emit(f"{op_name}完成，成功 {success_count}/{total} 个文件")
                
        except Exception as e:
            self.error.emit(str(e))
