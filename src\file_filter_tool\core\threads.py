"""
线程管理模块
包含各种后台任务线程
"""

from pathlib import Path
try:
    from PySide6.QtCore import QThread, Signal
except ImportError:
    print("PySide6 not available")

class FileSearchThread(QThread):
    """文件搜索线程"""
    
    finished = Signal(list)  # 搜索完成信号
    progress = Signal(str)   # 进度信号
    error = Signal(str)      # 错误信号
    
    def __init__(self, file_filter, **kwargs):
        super().__init__()
        self.file_filter = file_filter
        self.search_params = kwargs

        # 提取编号文件筛选设置
        self.numbered_file_settings = kwargs.pop('numbered_file_settings', None)
    
    def run(self):
        """执行搜索"""
        try:
            self.progress.emit("开始搜索文件...")
            files = self.file_filter.find_files(**self.search_params)
            self.progress.emit(f"初步搜索完成，找到 {len(files)} 个文件")

            # 应用编号文件筛选
            if self.numbered_file_settings and self.numbered_file_settings.get('enabled'):
                self.progress.emit("正在应用编号文件筛选...")
                files = self.apply_numbered_file_filter(files)
                self.progress.emit(f"编号文件筛选完成，保留 {len(files)} 个文件")

            self.progress.emit(f"搜索完成，最终结果 {len(files)} 个文件")
            self.finished.emit(files)
        except Exception as e:
            self.error.emit(str(e))

    def apply_numbered_file_filter(self, files):
        """应用编号文件筛选"""
        try:
            from .numbered_files import NumberedFileFilter

            numbered_filter = NumberedFileFilter()

            settings = self.numbered_file_settings
            filtered_files, stats = numbered_filter.filter_numbered_files(
                files,
                time_strategy=settings.get('strategy', 'keep_newest'),
                strategy_options=settings.get('options', {}),
                ignore_extension=settings.get('ignore_extension', True),
                case_sensitive=settings.get('case_sensitive', False)
            )

            # 输出统计信息
            if stats['numbered_groups'] > 0:
                self.progress.emit(f"检测到 {stats['numbered_groups']} 个编号文件组")
                self.progress.emit(f"编号文件: {stats['total_numbered_files']} 个，非编号文件: {stats['non_numbered_files']} 个")
                self.progress.emit(f"筛选后保留: {stats['total_kept_files']} 个，过滤: {stats['total_filtered_files']} 个")

            return filtered_files

        except Exception as e:
            self.progress.emit(f"编号文件筛选出错: {e}")
            return files  # 出错时返回原始文件列表

class FileOperationThread(QThread):
    """文件操作线程"""
    
    finished = Signal(str)   # 操作完成信号
    progress = Signal(str)   # 进度信号
    error = Signal(str)      # 错误信号
    
    def __init__(self, file_filter, operation, files, destination=None):
        super().__init__()
        self.file_filter = file_filter
        self.operation = operation
        self.files = files
        self.destination = destination
    
    def run(self):
        """执行文件操作"""
        try:
            total = len(self.files)
            operation_names = {
                "copy": "复制",
                "move": "移动", 
                "delete": "删除"
            }
            op_name = operation_names.get(self.operation, self.operation)
            
            self.progress.emit(f"开始{op_name} {total} 个文件...")
            
            success_count = 0
            for i, file_path in enumerate(self.files, 1):
                try:
                    if self.operation == "copy":
                        self.file_filter.copy_file(file_path, self.destination)
                    elif self.operation == "move":
                        self.file_filter.move_file(file_path, self.destination)
                    elif self.operation == "delete":
                        self.file_filter.delete_file(file_path)
                    
                    success_count += 1
                    
                    # 每处理10个文件报告一次进度
                    if i % 10 == 0 or i == total:
                        self.progress.emit(f"已{op_name} {i}/{total} 个文件")
                        
                except Exception as e:
                    self.progress.emit(f"处理文件失败: {file_path.name} - {e}")
            
            if success_count == total:
                self.finished.emit(f"成功{op_name} {success_count} 个文件")
            else:
                self.finished.emit(f"{op_name}完成，成功 {success_count}/{total} 个文件")
                
        except Exception as e:
            self.error.emit(str(e))
