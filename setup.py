#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 安装脚本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "docs" / "使用指南.md"
long_description = ""
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    with open(requirements_file, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="file-filter-tool",
    version="1.0.0",
    author="Andy_127【浅醉丶墨语】",
    author_email="yangjun_127",
    description="一个功能强大的文件筛选和管理工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yangpingjun/file-filter-tool",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: System :: Filesystems",
        "Topic :: Utilities",
    ],
    python_requires=">=3.6",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.10.0",
            "black>=21.0.0",
            "flake8>=3.8.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "file-filter=file_filter_tool.gui_pyside6:main",
            "file-filter-cli=file_filter_tool.file_filter:main",
        ],
        "gui_scripts": [
            "file-filter-gui=file_filter_tool.gui_pyside6:main",
        ],
    },
    package_data={
        "file_filter_tool": ["../assets/res/*"],
    },
    include_package_data=True,
    zip_safe=False,
)
