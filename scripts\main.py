#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 命令行入口
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.file_filter_tool.file_filter import FileFilter

def parse_size(size_str: str) -> int:
    """解析大小字符串，支持 KB, MB, GB 单位"""
    if not size_str:
        return None
    
    size_str = size_str.upper()
    multipliers = {'B': 1, 'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
    
    for unit, multiplier in multipliers.items():
        if size_str.endswith(unit):
            try:
                return int(float(size_str[:-len(unit)]) * multiplier)
            except ValueError:
                raise ValueError(f"无效的大小格式: {size_str}")
    
    # 如果没有单位，假设是字节
    try:
        return int(size_str)
    except ValueError:
        raise ValueError(f"无效的大小格式: {size_str}")

def main():
    parser = argparse.ArgumentParser(
        description="文件筛选过滤工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py --path /home/<USER>/documents --ext .txt .py --list
  python main.py --path . --name "*.log" --days 7 --delete
  python main.py --ext .jpg .png --min-size 1MB --copy ./images/
  python main.py --max-size 100KB --move ./small_files/
        """
    )
    
    # 基本参数
    parser.add_argument('--path', '-p', default='.',
                       help='搜索路径 (默认: 当前目录)')
    parser.add_argument('--recursive', '-r', action='store_true',
                       help='递归搜索子目录')
    
    # 筛选条件
    parser.add_argument('--ext', '--extensions', nargs='+',
                       help='文件扩展名 (如: .txt .py .jpg)')
    parser.add_argument('--name', '--pattern',
                       help='文件名模式 (支持正则表达式)')
    parser.add_argument('--min-size',
                       help='最小文件大小 (如: 1KB, 10MB)')
    parser.add_argument('--max-size',
                       help='最大文件大小 (如: 100MB, 1GB)')
    parser.add_argument('--days', type=int,
                       help='修改时间在指定天数内')
    
    # 操作选项
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--list', '-l', action='store_true',
                             help='列出符合条件的文件')
    action_group.add_argument('--copy', metavar='DEST',
                             help='复制文件到指定目录')
    action_group.add_argument('--move', metavar='DEST',
                             help='移动文件到指定目录')
    action_group.add_argument('--delete', action='store_true',
                             help='删除符合条件的文件')
    
    # 其他选项
    parser.add_argument('--details', '-d', action='store_true',
                       help='显示文件详细信息 (仅用于 --list)')
    parser.add_argument('--no-confirm', action='store_true',
                       help='删除文件时不需要确认 (仅用于 --delete)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细日志')
    
    args = parser.parse_args()
    
    # 验证路径
    if not Path(args.path).exists():
        print(f"错误: 路径不存在: {args.path}")
        sys.exit(1)
    
    # 解析大小参数
    try:
        min_size = parse_size(args.min_size) if args.min_size else None
        max_size = parse_size(args.max_size) if args.max_size else None
    except ValueError as e:
        print(f"错误: {e}")
        sys.exit(1)
    
    # 创建文件筛选器
    file_filter = FileFilter(args.path)
    
    # 设置日志级别
    if args.verbose:
        import logging
        logging.getLogger('FileFilter').setLevel(logging.DEBUG)
    
    try:
        # 查找文件
        files = file_filter.find_files(
            extensions=args.ext,
            name_pattern=args.name,
            min_size=min_size,
            max_size=max_size,
            modified_days=args.days,
            recursive=args.recursive
        )
        
        # 执行操作
        if args.list:
            file_filter.list_files(files, show_details=args.details)
        elif args.copy:
            file_filter.copy_files(files, args.copy)
        elif args.move:
            file_filter.move_files(files, args.move)
        elif args.delete:
            file_filter.delete_files(files, confirm=not args.no_confirm)
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
