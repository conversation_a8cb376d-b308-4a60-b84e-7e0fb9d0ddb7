# 文件筛选工具 - 项目结构说明（重构版）

## 📁 项目目录结构

```
file-filter-tool/
├── 📄 项目配置文件
│   ├── setup.py              # 安装配置脚本
│   ├── requirements.txt      # Python依赖列表
│   ├── README.md            # 项目说明文档
│   └── sample_config.json   # 示例配置文件

├── 🚀 启动脚本
│   ├── run.py               # 主启动脚本 ⭐
│   ├── run_no_console.pyw   # 无窗口启动脚本 ⭐
│   └── scripts/             # 其他启动脚本
│       ├── 启动GUI.py       # 详细启动器
│       ├── 启动GUI无窗口.pyw # 无窗口启动器
│       └── main.py          # 命令行版本

├── 📦 源代码包
│   └── src/
│       └── file_filter_tool/
│           ├── __init__.py      # 包初始化文件
│           ├── file_filter.py   # 文件筛选核心逻辑 ⭐
│           ├── config.py        # 配置文件管理
│           ├── gui_pyside6.py   # PySide6现代界面 ⭐
│           └── gui.py           # tkinter传统界面

├── 🎨 资源文件
│   └── assets/
│       └── res/
│           ├── logo.ico     # Windows图标文件
│           └── logo.png     # 通用PNG图标

├── 📚 文档目录
│   └── docs/
│       ├── 使用指南.md      # 用户使用指南
│       ├── 项目结构说明.md  # 项目结构文档
│       ├── 菜单功能说明.md  # 菜单功能详解
│       ├── 新功能说明.md    # 新功能详细说明
│       └── 更新完成说明.md  # 更新说明文档

└── 🧪 测试目录
    └── tests/               # 单元测试文件（待添加）
```

## 🎯 主要文件说明

### 核心功能模块

**file_filter.py**
- 文件筛选的核心逻辑
- 支持多种筛选条件（扩展名、大小、时间、名称模式）
- 提供文件操作功能（复制、移动、删除）

**config.py**
- 配置文件管理
- 支持保存和加载筛选规则
- JSON格式配置存储

**main.py**
- 命令行界面入口
- 支持命令行参数
- 适合脚本化使用

### 图形界面模块

**gui_pyside6.py** ⭐ 推荐
- 基于Qt6的现代化界面
- 多线程操作，界面不卡顿
- 支持应用程序图标
- 功能最完整，体验最佳

**gui.py**
- 基于tkinter的传统界面
- 兼容性好，无需额外安装
- 功能相对简单

### 启动工具

**启动GUI.py** ⭐ 推荐启动方式
- 智能检测可用的GUI框架
- 自动选择最佳界面版本
- 包含完整的错误处理和帮助信息

**启动GUI无窗口.pyw** ⭐ 无窗口启动
- 隐藏命令行窗口，提供专业软件体验
- 使用.pyw扩展名，Windows系统自动无窗口运行
- 图形化错误提示，完善的异常处理

### 配置文件

**sample_config.json**
- 包含多种实用的筛选配置示例
- 可直接使用或作为模板修改
- 涵盖常见的文件管理场景

## 🚀 推荐使用方式

### 1. 无窗口启动（推荐）
```bash
# 双击文件或命令行启动
启动GUI无窗口.pyw
```
- 隐藏命令行窗口，专业软件体验
- 自动检测并启动最佳界面

### 2. 快速启动（传统方式）
```bash
python 启动GUI.py
```
- 自动检测并启动最佳界面
- 包含完整的错误处理

### 3. 直接启动现代界面
```bash
python gui_pyside6.py
```
- 直接启动PySide6界面
- 需要确保PySide6已安装

### 4. 命令行使用
```bash
python main.py --help
```
- 查看命令行参数
- 适合脚本化和自动化



## 🔧 环境要求

### 基本要求
- Python 3.6+
- 标准库模块（pathlib, json, re等）

### GUI界面要求
- **PySide6界面**：需要安装 `pip install PySide6`
- **tkinter界面**：通常随Python自带

### 可选依赖
- PyYAML：YAML配置文件支持
- pytest：运行单元测试
- tqdm：进度条显示

## 📝 使用建议

1. **日常使用**：使用 `python 启动GUI.py` 启动
2. **配置管理**：参考 `sample_config.json` 创建自定义配置
3. **问题诊断**：查看 `使用指南.md` 获取详细帮助
4. **菜单功能**：使用丰富的菜单栏功能提高效率

## 🎨 界面特色

- ✅ 现代化Qt6界面设计
- ✅ 应用程序图标支持
- ✅ 多线程文件操作
- ✅ 实时进度显示
- ✅ 配置保存和加载
- ✅ 完善的错误处理
- ✅ 响应式布局设计
- ✅ 重复文件检测功能
- ✅ 无窗口启动支持

这个项目结构经过优化，删除了无关文件，保留了所有核心功能和实用工具，为用户提供了完整而简洁的文件筛选解决方案。
