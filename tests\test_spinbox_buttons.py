#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpinBox按钮UI修复测试
专门测试增加和减少按钮的颜色修复效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QSpinBox
    )
    from PySide6.QtCore import QTimer
    from file_filter_tool.ui.components import (
        AdvancedFilterWidget, NumberedFileFilterWidget, FilterConditionsWidget
    )
    from file_filter_tool.ui.themes import WindowsTheme, VSTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class SpinBoxButtonTestWidget(QWidget):
    """SpinBox按钮测试控件"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "windows"
        self.spinboxes = []
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("SpinBox按钮UI修复测试")
        self.setMinimumSize(700, 600)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🔧 SpinBox按钮UI修复测试

本测试专门验证SpinBox增加/减少按钮的UI修复效果：

🎯 测试重点：
• 按钮背景颜色是否正确
• 悬停效果是否正常（蓝色背景）
• 按下效果是否正常（深蓝色背景）
• 箭头颜色是否清晰可见（白色箭头）
• 不同主题下的显示效果

💡 操作方法：
• 点击SpinBox的增加/减少按钮
• 悬停在按钮上观察颜色变化
• 切换主题观察不同主题下的效果
• 检查按钮宽度是否合适（20px）

✅ 预期效果：
• 默认：灰色背景，深色箭头
• 悬停：蓝色背景，白色箭头
• 按下：深蓝色背景，白色箭头
        """)
        layout.addWidget(info_label)
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.theme_btn = QPushButton("切换到 VS 主题")
        self.theme_btn.clicked.connect(self.switch_theme)
        theme_layout.addWidget(self.theme_btn)
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 测试组1：基本SpinBox
        basic_group = QGroupBox("基本SpinBox测试")
        basic_layout = QVBoxLayout(basic_group)
        
        # 创建不同类型的SpinBox
        spinbox_tests = [
            ("基本数字输入", 0, 100, 10, ""),
            ("带后缀的数字", 1, 999, 5, " 个"),
            ("百分比输入", 0, 100, 50, " %"),
            ("天数输入", 1, 365, 7, " 天"),
            ("大范围数字", 0, 9999, 100, ""),
        ]
        
        for label_text, min_val, max_val, default_val, suffix in spinbox_tests:
            row_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setMinimumWidth(120)
            row_layout.addWidget(label)
            
            spinbox = QSpinBox()
            spinbox.setRange(min_val, max_val)
            spinbox.setValue(default_val)
            if suffix:
                spinbox.setSuffix(suffix)
            spinbox.setMinimumWidth(80)
            spinbox.setMaximumWidth(100)
            row_layout.addWidget(spinbox)
            
            self.spinboxes.append(spinbox)
            
            row_layout.addStretch()
            basic_layout.addLayout(row_layout)
        
        layout.addWidget(basic_group)
        
        # 测试组2：实际组件中的SpinBox
        components_group = QGroupBox("实际组件中的SpinBox")
        components_layout = QVBoxLayout(components_group)
        
        # 筛选条件组件
        filter_label = QLabel("筛选条件组件:")
        components_layout.addWidget(filter_label)
        self.filter_conditions = FilterConditionsWidget()
        components_layout.addWidget(self.filter_conditions)
        
        # 高级筛选组件
        advanced_label = QLabel("高级筛选组件:")
        components_layout.addWidget(advanced_label)
        self.advanced_filter = AdvancedFilterWidget()
        components_layout.addWidget(self.advanced_filter)
        
        # 编号文件筛选组件
        numbered_label = QLabel("编号文件筛选组件:")
        components_layout.addWidget(numbered_label)
        self.numbered_filter = NumberedFileFilterWidget()
        components_layout.addWidget(self.numbered_filter)
        
        layout.addWidget(components_group)
        
        # 测试控制按钮
        control_layout = QHBoxLayout()
        
        reset_btn = QPushButton("重置所有数值")
        reset_btn.clicked.connect(self.reset_values)
        control_layout.addWidget(reset_btn)
        
        random_btn = QPushButton("随机设置数值")
        random_btn.clicked.connect(self.set_random_values)
        control_layout.addWidget(random_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
    
    def apply_theme(self):
        """应用主题"""
        if self.current_theme == "windows":
            self.setStyleSheet(WindowsTheme.get_complete_style())
        else:
            self.setStyleSheet(VSTheme.get_complete_style())
    
    def switch_theme(self):
        """切换主题"""
        if self.current_theme == "windows":
            self.current_theme = "vs"
            self.theme_btn.setText("切换到 Windows 主题")
            print("🌙 切换到 Visual Studio 深色主题")
        else:
            self.current_theme = "windows"
            self.theme_btn.setText("切换到 VS 主题")
            print("☀️ 切换到 Windows 浅色主题")
        
        self.apply_theme()
    
    def reset_values(self):
        """重置所有数值"""
        for spinbox in self.spinboxes:
            spinbox.setValue(spinbox.minimum())
        
        # 重置组件中的SpinBox
        self.filter_conditions.days_spin.setValue(0)
        self.advanced_filter.min_duplicate_spin.setValue(2)
        self.advanced_filter.min_common_chars_spin.setValue(5)
        self.advanced_filter.similarity_spin.setValue(70)
        self.numbered_filter.count_spin.setValue(1)
        self.numbered_filter.interval_spin.setValue(7)
        
        print("🔄 所有数值已重置")
    
    def set_random_values(self):
        """随机设置数值"""
        import random
        
        for spinbox in self.spinboxes:
            random_value = random.randint(spinbox.minimum(), spinbox.maximum())
            spinbox.setValue(random_value)
        
        # 随机设置组件中的SpinBox
        self.filter_conditions.days_spin.setValue(random.randint(0, 365))
        self.advanced_filter.min_duplicate_spin.setValue(random.randint(2, 10))
        self.advanced_filter.min_common_chars_spin.setValue(random.randint(3, 20))
        self.advanced_filter.similarity_spin.setValue(random.randint(50, 100))
        self.numbered_filter.count_spin.setValue(random.randint(1, 10))
        self.numbered_filter.interval_spin.setValue(random.randint(1, 30))
        
        print("🎲 所有数值已随机设置")

def test_spinbox_buttons():
    """测试SpinBox按钮"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行SpinBox按钮测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🔧 SpinBox按钮UI修复测试启动")
    print("=" * 50)
    print("📋 测试内容:")
    print("  • SpinBox增加/减少按钮的背景颜色")
    print("  • 按钮悬停效果（蓝色背景）")
    print("  • 按钮按下效果（深蓝色背景）")
    print("  • 箭头颜色显示（白色箭头）")
    print("  • 按钮宽度优化（20px）")
    print("  • 不同主题下的显示效果")
    print()
    print("💡 测试方法:")
    print("  • 点击SpinBox的+/-按钮")
    print("  • 悬停在按钮上观察颜色变化")
    print("  • 切换主题观察样式变化")
    print("  • 检查按钮是否容易点击")
    print()
    print("🎯 预期结果:")
    print("  • 默认状态：灰色背景，深色箭头")
    print("  • 悬停状态：蓝色背景，白色箭头")
    print("  • 按下状态：深蓝色背景，白色箭头")
    print("  • 按钮宽度合适，容易点击")
    print()
    
    # 创建测试窗口
    test_widget = SpinBoxButtonTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行SpinBox按钮测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_spinbox_buttons()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
