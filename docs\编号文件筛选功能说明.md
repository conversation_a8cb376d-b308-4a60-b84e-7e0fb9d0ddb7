# 编号文件筛选功能说明

## 🎯 功能概述

编号文件筛选功能是文件筛选工具v1.2的新增高级功能，专门用于处理包含相同阿拉伯数字编号的文件，可以根据时间策略筛选出需要保留的文件版本。

## 🔍 适用场景

### 典型使用场景
- **文档版本管理**: `report_001.pdf`, `report_002.pdf`, `report_003.pdf`
- **图片序列处理**: `IMG_001.jpg`, `IMG_002.jpg`, `IMG_003.jpg`
- **软件版本清理**: `app_v1.0.exe`, `app_v1.1.exe`, `app_v2.0.exe`
- **备份文件管理**: `backup_20240101.zip`, `backup_20240102.zip`
- **日志文件清理**: `log_001.txt`, `log_002.txt`, `log_003.txt`

### 解决的问题
- 同一文件的多个版本占用存储空间
- 需要快速找到最新或最旧的文件版本
- 按时间间隔保留重要的历史版本
- 批量清理过期的编号文件

## 🎨 界面位置

编号文件筛选功能位于主界面的**高级筛选**区域，包含以下控件：

```
┌─ 高级筛选 ─────────────────────────────────────┐
│ 重复文件: [✓] 仅显示重复文件  最少重复: [2] 个    │
│ 文件名相似: [✓] 仅显示相似文件名  相同字符: [5] 个 │
│ ──────────────────────────────────────────── │
│ 编号文件筛选: [✓] 启用编号文件时间筛选           │
│ 时间策略: [保留最新 ▼] 数量: [1] 个 间隔: [7] 天 │
│ 高级选项: [✓] 忽略扩展名 [ ] 区分大小写         │
└─────────────────────────────────────────────┘
```

## ⚙️ 功能设置

### 1. 启用开关
- **编号文件筛选**: 勾选以启用此功能
- 未勾选时，此功能不会影响搜索结果

### 2. 时间策略选项

#### A. 保留最新 (默认)
- **说明**: 每组编号文件中只保留修改时间最新的文件
- **适用**: 只需要最新版本的场景
- **示例**: `report_001.pdf`, `report_002.pdf`, `report_003.pdf` → 保留 `report_003.pdf`

#### B. 保留最旧
- **说明**: 每组编号文件中只保留修改时间最旧的文件
- **适用**: 需要保留原始版本的场景
- **示例**: `backup_001.zip`, `backup_002.zip`, `backup_003.zip` → 保留 `backup_001.zip`

#### C. 保留最新N个
- **说明**: 每组编号文件中保留修改时间最新的N个文件
- **设置**: 通过"数量"输入框设置保留的文件数量
- **适用**: 需要保留最近几个版本的场景
- **示例**: 设置保留2个，`img_001.jpg`, `img_002.jpg`, `img_003.jpg` → 保留 `img_002.jpg`, `img_003.jpg`

#### D. 保留最旧N个
- **说明**: 每组编号文件中保留修改时间最旧的N个文件
- **设置**: 通过"数量"输入框设置保留的文件数量
- **适用**: 需要保留早期版本的场景

#### E. 保留时间范围
- **说明**: 保留指定时间范围内的文件
- **设置**: 需要在代码中设置时间范围（未来版本将添加日期选择器）
- **适用**: 特定时间段的文件管理

#### F. 按间隔保留
- **说明**: 按指定的时间间隔保留文件
- **设置**: 通过"间隔"输入框设置间隔天数
- **适用**: 需要定期保留历史版本的场景
- **示例**: 设置7天间隔，会保留每7天内的第一个文件

### 3. 高级选项

#### A. 忽略扩展名 (默认启用)
- **启用**: `file_001.txt` 和 `file_001.pdf` 视为同一组
- **禁用**: 不同扩展名的文件分别分组

#### B. 区分大小写 (默认禁用)
- **启用**: `File_001.txt` 和 `file_001.txt` 视为不同组
- **禁用**: 忽略大小写差异

## 🔧 编号检测规则

### 支持的编号模式

#### 1. 后缀编号 (最常见)
```
file_001.txt    → 基础名: "file", 编号: "001"
image_002.jpg   → 基础名: "image", 编号: "002"
report_123.pdf  → 基础名: "report", 编号: "123"
```

#### 2. 前缀编号
```
001_document.doc → 基础名: "document", 编号: "001"
123_photo.jpg    → 基础名: "photo", 编号: "123"
```

#### 3. 版本编号
```
app_v1.0.exe     → 基础名: "app", 编号: "1.0"
software_v2.1.zip → 基础名: "software", 编号: "2.1"
```

#### 4. 日期编号
```
backup_20240101.zip → 基础名: "backup", 编号: "20240101"
log_2024-01-01.txt  → 基础名: "log", 编号: "2024-01-01"
```

#### 5. 中间编号
```
file_001_final.txt → 基础名: "file_final", 编号: "001"
doc_123_temp.pdf   → 基础名: "doc_temp", 编号: "123"
```

#### 6. 时间戳编号
```
data_1704067200.json → 基础名: "data", 编号: "1704067200"
```

## 📊 工作流程

### 1. 文件检测阶段
```
输入文件列表 → 编号模式识别 → 提取基础名称和编号
```

### 2. 分组阶段
```
相同基础名称的文件 → 归为一组 → 记录每个文件的时间信息
```

### 3. 筛选阶段
```
每个分组 → 应用时间策略 → 选择保留的文件
```

### 4. 结果合并
```
保留的编号文件 + 非编号文件 → 最终结果列表
```

## 💡 使用示例

### 示例1：清理文档版本
```
输入文件:
├── contract_v1.0.pdf (2024-01-01)
├── contract_v1.1.pdf (2024-01-05)
├── contract_v2.0.pdf (2024-01-10) ← 最新
└── other_file.txt (2024-01-03)

设置: 启用编号文件筛选 + 保留最新

结果:
├── contract_v2.0.pdf ✅ 保留
└── other_file.txt ✅ 保留 (非编号文件)

过滤:
├── contract_v1.0.pdf ❌ 过滤
└── contract_v1.1.pdf ❌ 过滤
```

### 示例2：保留多个版本
```
输入文件:
├── image_001.jpg (2024-01-01)
├── image_002.jpg (2024-01-05)
├── image_003.jpg (2024-01-10)
├── image_004.jpg (2024-01-15) ← 最新
└── readme.txt (2024-01-12)

设置: 启用编号文件筛选 + 保留最新2个

结果:
├── image_003.jpg ✅ 保留
├── image_004.jpg ✅ 保留
└── readme.txt ✅ 保留 (非编号文件)

过滤:
├── image_001.jpg ❌ 过滤
└── image_002.jpg ❌ 过滤
```

### 示例3：按间隔保留
```
输入文件:
├── backup_001.zip (2024-01-01)
├── backup_002.zip (2024-01-03)
├── backup_003.zip (2024-01-05)
├── backup_004.zip (2024-01-08) ← 超过7天间隔
└── backup_005.zip (2024-01-10)

设置: 启用编号文件筛选 + 按间隔保留 (7天)

结果:
├── backup_001.zip ✅ 保留 (第一个)
└── backup_004.zip ✅ 保留 (间隔>7天)

过滤:
├── backup_002.zip ❌ 过滤
├── backup_003.zip ❌ 过滤
└── backup_005.zip ❌ 过滤
```

## 📈 统计信息

搜索完成后，操作日志会显示详细的统计信息：

```
检测到 3 个编号文件组
编号文件: 9 个，非编号文件: 2 个
筛选后保留: 5 个，过滤: 6 个
```

## ⚠️ 注意事项

### 1. 文件安全
- 此功能只影响搜索结果显示，不会直接删除文件
- 实际的文件操作（删除、移动）需要用户确认

### 2. 性能考虑
- 大量文件时，编号检测可能需要一些时间
- 建议先用其他条件缩小文件范围

### 3. 编号识别
- 无法识别编号的文件会被保留（不受此功能影响）
- 复杂的编号模式可能无法正确识别

### 4. 时间依据
- 筛选基于文件的修改时间 (mtime)
- 确保文件时间信息准确

## 🔄 与其他功能的配合

### 1. 基础筛选
- 先应用扩展名、大小、时间等基础筛选
- 再对结果应用编号文件筛选

### 2. 重复文件检测
- 可以同时启用重复文件检测
- 两个功能独立工作，互不冲突

### 3. 文件名相似度
- 可以同时启用文件名相似度检测
- 编号文件筛选优先级更高

## 🎯 最佳实践

### 1. 推荐设置
- **日常使用**: 保留最新 + 忽略扩展名
- **版本管理**: 保留最新N个 (N=2或3)
- **备份清理**: 按间隔保留 (7天或30天)

### 2. 操作建议
- 先预览筛选结果，确认无误后再执行文件操作
- 重要文件建议先备份
- 定期使用此功能清理编号文件

### 3. 故障排除
- 如果编号未被识别，检查文件名格式
- 如果结果不符合预期，检查时间策略设置
- 如果性能较慢，先用其他条件缩小范围

---

**功能版本**: v1.2  
**开发时间**: 2024年  
**开发者**: Andy_127【浅醉丶墨语】  
**联系方式**: yangjun_127

🎉 **享受智能的编号文件管理体验！**
