"""
UI模块
包含用户界面相关的组件和主题
"""

from .themes import WindowsTheme
from .components import (
    SearchPathWidget,
    FilterConditionsWidget,
    AdvancedFilterWidget,
    OperationButtonsWidget,
    LogWidget,
    CustomCheckBox,
    NumberedFileFilterWidget
)

# 新的标签页组件
try:
    from .main_tabbed_interface import MainTabbedInterface
    from .normal_search_tab import NormalSearchTab
    from .dual_directory_tab import DualDirectoryTab
    from .duplicate_cleanup_tab import DuplicateCleanupTab
    from .batch_operations_tab import BatchOperationsTab
    TABBED_INTERFACE_AVAILABLE = True
except ImportError:
    TABBED_INTERFACE_AVAILABLE = False

__all__ = [
    'WindowsTheme',
    'SearchPathWidget',
    'FilterConditionsWidget',
    'AdvancedFilterWidget',
    'OperationButtonsWidget',
    'LogWidget',
    'CustomCheckBox',
    'NumberedFileFilterWidget'
]

# 如果标签页组件可用，添加到导出列表
if TABBED_INTERFACE_AVAILABLE:
    __all__.extend([
        'MainTabbedInterface',
        'NormalSearchTab',
        'DualDirectoryTab',
        'DuplicateCleanupTab',
        'BatchOperationsTab'
    ])
