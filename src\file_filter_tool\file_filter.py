#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选过滤器
支持多种筛选条件和操作
"""

import os
import shutil
import glob
import re
import hashlib
import difflib
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict, Counter
import logging

# 尝试导入fuzzywuzzy，如果没有安装则使用difflib作为备选
try:
    from fuzzywuzzy import fuzz
    FUZZYWUZZY_AVAILABLE = True
except ImportError:
    FUZZYWUZZY_AVAILABLE = False

class FileFilter:
    """文件筛选过滤器类"""
    
    def __init__(self, base_path: str = "."):
        """
        初始化文件筛选器
        
        Args:
            base_path: 基础搜索路径
        """
        self.base_path = Path(base_path).resolve()
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FileFilter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def find_files(self,
                   extensions: Optional[List[str]] = None,
                   name_pattern: Optional[str] = None,
                   min_size: Optional[int] = None,
                   max_size: Optional[int] = None,
                   modified_days: Optional[int] = None,
                   start_date = None,
                   end_date = None,
                   recursive: bool = True,
                   find_duplicates: bool = False,
                   min_duplicate_count: int = 2,
                   find_similar_names: bool = False,
                   min_common_chars: int = 5,
                   similarity_threshold: int = 70) -> List[Path]:
        """
        根据条件筛选文件

        Args:
            extensions: 文件扩展名列表，如 ['.txt', '.py']
            name_pattern: 文件名模式（支持通配符）
            min_size: 最小文件大小（字节）
            max_size: 最大文件大小（字节）
            modified_days: 修改时间（天数内）
            recursive: 是否递归搜索子目录
            find_duplicates: 是否只查找重复文件
            min_duplicate_count: 最少重复文件数量
            find_similar_names: 是否查找文件名相似的文件
            min_common_chars: 最少相同字符数量
            similarity_threshold: 相似度阈值（0-100）

        Returns:
            符合条件的文件路径列表
        """
        files = []

        # 获取所有文件
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"

        for file_path in self.base_path.glob(pattern):
            if file_path.is_file():
                if self._match_criteria(file_path, extensions, name_pattern,
                                      min_size, max_size, modified_days, start_date, end_date):
                    files.append(file_path)

        # 如果需要查找重复文件
        if find_duplicates:
            files = self._find_duplicate_files(files, min_duplicate_count)

        # 如果需要查找文件名相似的文件
        if find_similar_names:
            files = self._find_similar_name_files(files, min_common_chars, similarity_threshold)

        self.logger.info(f"找到 {len(files)} 个符合条件的文件")
        return files
    
    def _match_criteria(self, file_path: Path,
                       extensions: Optional[List[str]],
                       name_pattern: Optional[str],
                       min_size: Optional[int],
                       max_size: Optional[int],
                       modified_days: Optional[int],
                       start_date = None,
                       end_date = None) -> bool:
        """检查文件是否符合筛选条件"""
        
        # 检查扩展名
        if extensions:
            if file_path.suffix.lower() not in [ext.lower() for ext in extensions]:
                return False
        
        # 检查文件名模式
        if name_pattern:
            if not re.search(name_pattern, file_path.name, re.IGNORECASE):
                return False
        
        # 检查文件大小
        try:
            file_size = file_path.stat().st_size
            if min_size and file_size < min_size:
                return False
            if max_size and file_size > max_size:
                return False
        except OSError:
            return False
        
        # 检查修改时间
        try:
            modified_time = datetime.fromtimestamp(file_path.stat().st_mtime)

            # 如果指定了天数限制（向后兼容）
            if modified_days:
                cutoff_time = datetime.now() - timedelta(days=modified_days)
                if modified_time < cutoff_time:
                    return False

            # 如果指定了日期范围
            if start_date and end_date:
                # 将日期转换为datetime对象进行比较
                start_datetime = datetime.combine(start_date, datetime.min.time())
                end_datetime = datetime.combine(end_date, datetime.max.time())

                if not (start_datetime <= modified_time <= end_datetime):
                    return False

        except OSError:
            return False
        
        return True

    def _calculate_file_hash(self, file_path: Path, chunk_size: int = 8192) -> str:
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(chunk_size), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except (OSError, IOError):
            return ""

    def _find_duplicate_files(self, files: List[Path], min_count: int = 2) -> List[Path]:
        """查找重复文件（增强版）"""
        if not files:
            return []

        self.logger.info(f"开始检测重复文件，共 {len(files)} 个文件")

        # 第一步：按文件大小分组（快速预筛选）
        size_groups = defaultdict(list)
        for file_path in files:
            try:
                size = file_path.stat().st_size
                # 跳过空文件（除非特别要求）
                if size > 0:
                    size_groups[size].append(file_path)
            except OSError:
                continue

        self.logger.info(f"按大小分组完成，{len(size_groups)} 个不同大小")

        # 第二步：对于相同大小的文件，先计算部分哈希（快速检测）
        potential_duplicates = []
        for size, file_list in size_groups.items():
            if len(file_list) >= min_count:
                potential_duplicates.extend(file_list)

        if not potential_duplicates:
            self.logger.info("未发现潜在重复文件")
            return []

        self.logger.info(f"发现 {len(potential_duplicates)} 个潜在重复文件，开始计算哈希值")

        # 第三步：计算完整哈希值
        hash_groups = defaultdict(list)
        processed = 0
        for file_path in potential_duplicates:
            file_hash = self._calculate_file_hash(file_path)
            if file_hash:
                hash_groups[file_hash].append(file_path)
            processed += 1
            if processed % 100 == 0:
                self.logger.info(f"已处理 {processed}/{len(potential_duplicates)} 个文件")

        # 第四步：收集真正的重复文件
        duplicate_files = []
        duplicate_groups = 0
        for file_hash, file_list in hash_groups.items():
            if len(file_list) >= min_count:
                duplicate_files.extend(file_list)
                duplicate_groups += 1

        self.logger.info(f"检测完成：发现 {len(duplicate_files)} 个重复文件，分为 {duplicate_groups} 组")
        return duplicate_files

    def get_duplicate_groups(self, files: List[Path]) -> Dict[str, List[Path]]:
        """获取重复文件分组信息（增强版）"""
        if not files:
            return {}

        self.logger.info(f"开始分析重复文件分组，共 {len(files)} 个文件")

        # 按文件大小分组（快速预筛选）
        size_groups = defaultdict(list)
        for file_path in files:
            try:
                size = file_path.stat().st_size
                if size > 0:  # 跳过空文件
                    size_groups[size].append(file_path)
            except OSError:
                continue

        # 对于相同大小的文件，计算哈希值
        hash_groups = defaultdict(list)
        total_candidates = sum(len(file_list) for file_list in size_groups.values() if len(file_list) >= 2)
        processed = 0

        for size, file_list in size_groups.items():
            if len(file_list) >= 2:  # 只处理可能重复的文件
                for file_path in file_list:
                    file_hash = self._calculate_file_hash(file_path)
                    if file_hash:  # 只有成功计算哈希的文件才加入
                        hash_groups[file_hash].append(file_path)
                    processed += 1
                    if processed % 50 == 0:
                        self.logger.info(f"分组进度: {processed}/{total_candidates}")

        # 只返回真正重复的分组，并按文件数量排序
        duplicate_groups = {}
        for file_hash, file_list in hash_groups.items():
            if len(file_list) >= 2:
                # 按修改时间排序，最新的在前
                file_list.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                duplicate_groups[file_hash] = file_list

        self.logger.info(f"分组完成：发现 {len(duplicate_groups)} 个重复文件组")
        return duplicate_groups

    def get_duplicate_statistics(self, duplicate_groups: Dict[str, List[Path]]) -> Dict[str, Any]:
        """获取重复文件统计信息"""
        if not duplicate_groups:
            return {}

        total_files = sum(len(file_list) for file_list in duplicate_groups.values())
        total_groups = len(duplicate_groups)

        # 计算可节省的空间（保留每组中最新的一个文件）
        total_size = 0
        wasted_size = 0

        for file_list in duplicate_groups.values():
            if file_list:
                try:
                    file_size = file_list[0].stat().st_size
                    total_size += file_size * len(file_list)
                    wasted_size += file_size * (len(file_list) - 1)
                except OSError:
                    continue

        return {
            'total_files': total_files,
            'total_groups': total_groups,
            'total_size': total_size,
            'wasted_size': wasted_size,
            'space_saving_ratio': (wasted_size / total_size * 100) if total_size > 0 else 0
        }

    def _calculate_similarity(self, name1: str, name2: str) -> int:
        """计算两个文件名的相似度"""
        if FUZZYWUZZY_AVAILABLE:
            return fuzz.ratio(name1.lower(), name2.lower())
        else:
            # 使用difflib作为备选
            return int(difflib.SequenceMatcher(None, name1.lower(), name2.lower()).ratio() * 100)

    def _count_common_chars(self, name1: str, name2: str) -> int:
        """计算两个文件名的相同字符数量"""
        # 移除扩展名，只比较文件名部分
        name1_stem = Path(name1).stem.lower()
        name2_stem = Path(name2).stem.lower()

        # 计算相同字符的数量
        counter1 = Counter(name1_stem)
        counter2 = Counter(name2_stem)

        # 计算交集中字符的最小出现次数之和
        common_chars = 0
        for char in counter1:
            if char in counter2:
                common_chars += min(counter1[char], counter2[char])

        return common_chars

    def _find_similar_name_files(self, files: List[Path], min_common_chars: int = 5,
                                similarity_threshold: int = 70) -> List[Path]:
        """查找文件名相似的文件"""
        similar_files = []
        processed = set()

        for i, file1 in enumerate(files):
            if file1 in processed:
                continue

            similar_group = [file1]

            for j, file2 in enumerate(files[i+1:], i+1):
                if file2 in processed:
                    continue

                # 计算相同字符数量
                common_chars = self._count_common_chars(file1.name, file2.name)

                # 计算相似度
                similarity = self._calculate_similarity(file1.name, file2.name)

                # 如果满足条件，加入相似组
                if common_chars >= min_common_chars and similarity >= similarity_threshold:
                    similar_group.append(file2)
                    processed.add(file2)

            # 如果找到相似文件，加入结果
            if len(similar_group) > 1:
                similar_files.extend(similar_group)
                processed.update(similar_group)

        return similar_files

    def get_similar_name_groups(self, files: List[Path], min_common_chars: int = 5,
                               similarity_threshold: int = 70) -> Dict[str, List[Path]]:
        """获取文件名相似的文件分组"""
        groups = {}
        processed = set()
        group_id = 1

        for i, file1 in enumerate(files):
            if file1 in processed:
                continue

            similar_group = [file1]

            for j, file2 in enumerate(files[i+1:], i+1):
                if file2 in processed:
                    continue

                # 计算相同字符数量
                common_chars = self._count_common_chars(file1.name, file2.name)

                # 计算相似度
                similarity = self._calculate_similarity(file1.name, file2.name)

                # 如果满足条件，加入相似组
                if common_chars >= min_common_chars and similarity >= similarity_threshold:
                    similar_group.append(file2)
                    processed.add(file2)

            # 如果找到相似文件，创建分组
            if len(similar_group) > 1:
                group_key = f"similar_group_{group_id:03d}"
                groups[group_key] = similar_group
                processed.update(similar_group)
                group_id += 1

        return groups

    def list_files(self, files: List[Path], show_details: bool = False) -> None:
        """列出文件信息"""
        if not files:
            print("没有找到符合条件的文件")
            return
            
        print(f"\n找到 {len(files)} 个文件:")
        print("-" * 80)
        
        for file_path in files:
            if show_details:
                try:
                    stat = file_path.stat()
                    size = self._format_size(stat.st_size)
                    modified = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    print(f"{file_path.relative_to(self.base_path)} | {size} | {modified}")
                except OSError:
                    print(f"{file_path.relative_to(self.base_path)} | 无法获取详细信息")
            else:
                print(file_path.relative_to(self.base_path))
    
    def copy_files(self, files: List[Path], destination: str) -> None:
        """复制文件到目标目录"""
        dest_path = Path(destination)
        dest_path.mkdir(parents=True, exist_ok=True)
        
        success_count = 0
        for file_path in files:
            try:
                dest_file = dest_path / file_path.name
                # 如果目标文件已存在，添加数字后缀
                counter = 1
                while dest_file.exists():
                    name_parts = file_path.stem, counter, file_path.suffix
                    dest_file = dest_path / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                    counter += 1
                
                shutil.copy2(file_path, dest_file)
                success_count += 1
                self.logger.info(f"复制: {file_path} -> {dest_file}")
            except Exception as e:
                self.logger.error(f"复制失败 {file_path}: {e}")
        
        print(f"成功复制 {success_count}/{len(files)} 个文件到 {destination}")
    
    def move_files(self, files: List[Path], destination: str) -> None:
        """移动文件到目标目录"""
        dest_path = Path(destination)
        dest_path.mkdir(parents=True, exist_ok=True)
        
        success_count = 0
        for file_path in files:
            try:
                dest_file = dest_path / file_path.name
                # 如果目标文件已存在，添加数字后缀
                counter = 1
                while dest_file.exists():
                    name_parts = file_path.stem, counter, file_path.suffix
                    dest_file = dest_path / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                    counter += 1
                
                shutil.move(str(file_path), str(dest_file))
                success_count += 1
                self.logger.info(f"移动: {file_path} -> {dest_file}")
            except Exception as e:
                self.logger.error(f"移动失败 {file_path}: {e}")
        
        print(f"成功移动 {success_count}/{len(files)} 个文件到 {destination}")
    
    def delete_files(self, files: List[Path], confirm: bool = True) -> None:
        """删除文件"""
        if not files:
            print("没有文件需要删除")
            return
        
        if confirm:
            print(f"即将删除 {len(files)} 个文件:")
            for file_path in files[:5]:  # 只显示前5个
                print(f"  {file_path.relative_to(self.base_path)}")
            if len(files) > 5:
                print(f"  ... 还有 {len(files) - 5} 个文件")
            
            response = input("\n确认删除这些文件吗? (y/N): ")
            if response.lower() != 'y':
                print("取消删除操作")
                return
        
        success_count = 0
        for file_path in files:
            try:
                file_path.unlink()
                success_count += 1
                self.logger.info(f"删除: {file_path}")
            except Exception as e:
                self.logger.error(f"删除失败 {file_path}: {e}")
        
        print(f"成功删除 {success_count}/{len(files)} 个文件")
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
