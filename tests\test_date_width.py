#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改时间控件宽度测试
测试筛选条件中修改时间的两个日期选择器宽度调整
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QDateEdit
    )
    from PySide6.QtCore import QDate
    from file_filter_tool.ui.themes import WindowsTheme
    from file_filter_tool.ui.components import FilterConditionsWidget
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class DateWidthTestWidget(QWidget):
    """修改时间控件宽度测试"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("修改时间控件宽度测试 - 筛选条件中的日期选择器")
        self.setMinimumSize(900, 600)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
📅 修改时间控件宽度测试

本测试验证筛选条件中修改时间的两个日期选择器宽度调整：

📏 宽度调整：
• 开始日期：100-120px → 130-160px（+30-40px）
• 结束日期：100-120px → 130-160px（+30-40px）
• 增幅：约30-40%的宽度增加

✨ 美化效果：
• 上下布局的▲▼箭头
• 渐变背景按钮效果
• 24px宽的下拉按钮
• 清晰的下拉箭头符号

🎯 测试重点：
• 日期选择器是否有足够的显示空间
• "yyyy年MM月dd日"格式是否完整显示
• 下拉按钮是否美观且易于点击
• 日历弹出功能是否正常
• 整体布局是否协调

🔍 对比测试：
• 原始宽度 vs 加宽后的效果
• 不同日期格式的显示效果
• 各种交互状态的视觉反馈
        """)
        layout.addWidget(info_label)
        
        # 测试组1：实际的筛选条件组件
        real_group = QGroupBox("实际筛选条件组件（加宽后）")
        real_layout = QVBoxLayout(real_group)
        
        self.filter_conditions = FilterConditionsWidget()
        real_layout.addWidget(self.filter_conditions)
        
        layout.addWidget(real_group)
        
        # 测试组2：对比不同宽度的日期选择器
        compare_group = QGroupBox("宽度对比测试")
        compare_layout = QVBoxLayout(compare_group)
        
        # 原始宽度
        original_row = QHBoxLayout()
        original_row.addWidget(QLabel("原始宽度:"))
        
        original_start = QDateEdit()
        original_start.setCalendarPopup(True)
        original_start.setDate(QDate.currentDate().addDays(-30))
        original_start.setMinimumWidth(100)
        original_start.setMaximumWidth(120)
        original_start.setDisplayFormat("yyyy年MM月dd日")
        original_row.addWidget(original_start)
        
        original_row.addWidget(QLabel("至:"))
        
        original_end = QDateEdit()
        original_end.setCalendarPopup(True)
        original_end.setDate(QDate.currentDate())
        original_end.setMinimumWidth(100)
        original_end.setMaximumWidth(120)
        original_end.setDisplayFormat("yyyy年MM月dd日")
        original_row.addWidget(original_end)
        
        original_row.addWidget(QLabel("← 原始宽度 100-120px"))
        original_row.addStretch()
        compare_layout.addLayout(original_row)
        
        # 加宽后的宽度
        wide_row = QHBoxLayout()
        wide_row.addWidget(QLabel("加宽宽度:"))
        
        wide_start = QDateEdit()
        wide_start.setCalendarPopup(True)
        wide_start.setDate(QDate.currentDate().addDays(-30))
        wide_start.setMinimumWidth(130)
        wide_start.setMaximumWidth(160)
        wide_start.setDisplayFormat("yyyy年MM月dd日")
        wide_row.addWidget(wide_start)
        
        wide_row.addWidget(QLabel("至:"))
        
        wide_end = QDateEdit()
        wide_end.setCalendarPopup(True)
        wide_end.setDate(QDate.currentDate())
        wide_end.setMinimumWidth(130)
        wide_end.setMaximumWidth(160)
        wide_end.setDisplayFormat("yyyy年MM月dd日")
        wide_row.addWidget(wide_end)
        
        wide_row.addWidget(QLabel("← 加宽后 130-160px"))
        wide_row.addStretch()
        compare_layout.addLayout(wide_row)
        
        # 超宽测试
        extra_wide_row = QHBoxLayout()
        extra_wide_row.addWidget(QLabel("超宽测试:"))
        
        extra_wide_start = QDateEdit()
        extra_wide_start.setCalendarPopup(True)
        extra_wide_start.setDate(QDate.currentDate().addDays(-30))
        extra_wide_start.setMinimumWidth(180)
        extra_wide_start.setMaximumWidth(220)
        extra_wide_start.setDisplayFormat("yyyy年MM月dd日")
        extra_wide_row.addWidget(extra_wide_start)
        
        extra_wide_row.addWidget(QLabel("至:"))
        
        extra_wide_end = QDateEdit()
        extra_wide_end.setCalendarPopup(True)
        extra_wide_end.setDate(QDate.currentDate())
        extra_wide_end.setMinimumWidth(180)
        extra_wide_end.setMaximumWidth(220)
        extra_wide_end.setDisplayFormat("yyyy年MM月dd日")
        extra_wide_row.addWidget(extra_wide_end)
        
        extra_wide_row.addWidget(QLabel("← 超宽测试 180-220px"))
        extra_wide_row.addStretch()
        compare_layout.addLayout(extra_wide_row)
        
        layout.addWidget(compare_group)
        
        # 测试组3：不同日期格式测试
        format_group = QGroupBox("日期格式测试")
        format_layout = QVBoxLayout(format_group)
        
        formats = [
            ("标准格式", "yyyy年MM月dd日"),
            ("简短格式", "yyyy-MM-dd"),
            ("详细格式", "yyyy年MM月dd日 dddd"),
            ("英文格式", "MMM dd, yyyy"),
        ]
        
        for format_name, format_str in formats:
            format_row = QHBoxLayout()
            format_row.addWidget(QLabel(f"{format_name}:"))
            
            format_date = QDateEdit()
            format_date.setCalendarPopup(True)
            format_date.setDate(QDate.currentDate())
            format_date.setMinimumWidth(130)
            format_date.setMaximumWidth(160)
            format_date.setDisplayFormat(format_str)
            format_row.addWidget(format_date)
            
            format_row.addWidget(QLabel(f"← {format_str}"))
            format_row.addStretch()
            format_layout.addLayout(format_row)
        
        layout.addWidget(format_group)
        
        # 测试说明
        test_info = QLabel("""
📏 宽度调整效果说明：

✅ 加宽优势：
• 更充足的显示空间：中文日期格式完整显示
• 更好的视觉平衡：与其他控件宽度更协调
• 更易于操作：更大的点击区域
• 更美观的布局：避免文字被截断

🎨 美化效果：
• 下拉按钮：24px宽，与SpinBox保持一致
• 渐变背景：白色到浅灰色的优雅渐变
• 悬停效果：蓝色渐变背景
• 箭头符号：6px×8px的清晰下拉箭头

🔍 测试方法：
1. 观察日期文字是否完整显示，无截断
2. 点击下拉按钮，测试日历弹出功能
3. 悬停在下拉按钮上，观察渐变效果
4. 对比不同宽度的视觉效果
5. 测试不同日期格式的显示效果
        """)
        test_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(test_info)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(WindowsTheme.get_complete_style())

def test_date_width():
    """测试修改时间控件宽度"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行宽度测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("📅 修改时间控件宽度测试启动")
    print("=" * 50)
    print("📏 宽度调整:")
    print("  • 开始日期：100-120px → 130-160px")
    print("  • 结束日期：100-120px → 130-160px")
    print("  • 增幅：约30-40%的宽度增加")
    print()
    print("✨ 美化效果:")
    print("  • 下拉按钮：24px宽度，渐变背景")
    print("  • 箭头符号：6px×8px清晰下拉箭头")
    print("  • 悬停效果：蓝色渐变背景")
    print("  • 日期格式：yyyy年MM月dd日完整显示")
    print()
    print("🎯 测试重点:")
    print("  • 筛选条件中修改时间的两个日期选择器")
    print("  • 日期文字是否完整显示，无截断")
    print("  • 下拉按钮的美化效果")
    print("  • 日历弹出功能是否正常")
    print("  • 整体布局的协调性")
    print()
    print("🔍 验证方法:")
    print("  • 观察日期显示的完整性")
    print("  • 点击下拉按钮测试日历功能")
    print("  • 悬停测试渐变效果")
    print("  • 对比不同宽度的视觉效果")
    print()
    
    # 创建测试窗口
    test_widget = DateWidthTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行宽度测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_date_width()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
