# 文件筛选工具

一个功能强大的文件筛选和管理工具，支持多种筛选条件、重复文件检测、文件名相似度检测等功能。

## ✨ 主要功能

- 🔍 **多种筛选条件**：文件扩展名、大小、修改时间、名称模式
- 🔄 **重复文件检测**：基于MD5哈希值的精确重复文件检测
- 📝 **文件名相似度检测**：检测不同文件夹下文件名的相似性
- 📁 **文件操作**：复制、移动、删除文件
- ⚙️ **配置管理**：保存和加载筛选规则
- 🎨 **现代化界面**：基于PySide6的Qt6界面
- 🔇 **无窗口启动**：隐藏命令行窗口的专业启动方式

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动程序

```bash
# 方式1：推荐启动方式（无窗口）
python run_no_console.pyw

# 方式2：普通启动方式
python run.py

# 方式3：使用启动脚本
python scripts/启动GUI.py
```

## 📁 项目结构

```
file-filter-tool/
├── 📄 项目配置文件
│   ├── setup.py              # 安装配置
│   ├── requirements.txt      # 依赖列表
│   ├── README.md            # 项目说明
│   └── sample_config.json   # 示例配置
│
├── 🚀 启动脚本
│   ├── run.py               # 主启动脚本
│   ├── run_no_console.pyw   # 无窗口启动脚本 ⭐
│   └── scripts/             # 其他启动脚本
│       ├── 启动GUI.py       # 详细启动器
│       ├── 启动GUI无窗口.pyw # 无窗口启动器
│       └── main.py          # 命令行版本
│
├── 📦 源代码
│   └── src/
│       └── file_filter_tool/
│           ├── __init__.py      # 包初始化
│           ├── file_filter.py   # 核心筛选逻辑
│           ├── config.py        # 配置管理
│           ├── gui_pyside6.py   # PySide6界面 ⭐
│           └── gui.py           # tkinter界面
│
├── 🎨 资源文件
│   └── assets/
│       └── res/
│           ├── logo.ico     # Windows图标
│           └── logo.png     # 通用图标
│
├── 📚 文档
│   └── docs/
│       ├── 使用指南.md      # 用户指南
│       ├── 新功能说明.md    # 新功能详解
│       ├── 菜单功能说明.md  # 菜单功能
│       └── 项目结构说明.md  # 项目结构
│
└── 🧪 测试
    └── tests/               # 测试文件（待添加）
```

## 🔧 新功能详解

### 文件名相似度检测

检测不同文件夹下文件名的相似性，支持：

- **相同字符数量**：设置最少相同字符数量（3-50个）
- **相似度阈值**：设置相似度百分比（50-100%）
- **智能匹配**：使用fuzzywuzzy库进行模糊匹配
- **分组显示**：相似文件按组显示，便于管理

### 重复文件检测

基于MD5哈希值的精确重复文件检测：

- **分层检测**：先按文件大小预筛选，再计算哈希值
- **性能优化**：大文件分块计算，避免内存溢出
- **分组管理**：重复文件按组显示，支持批量操作

## 🎯 使用场景

1. **清理重复文件**：清理下载文件夹、备份文件夹中的重复文件
2. **整理相似文件**：找出文件名相似的文件进行分类整理
3. **文件筛选**：按条件筛选特定类型、大小、时间的文件
4. **批量操作**：对筛选结果进行批量复制、移动、删除

## 📋 系统要求

- **Python**: 3.6+
- **操作系统**: Windows, macOS, Linux
- **GUI框架**: PySide6 (推荐) 或 tkinter

## 🔧 开发

### 安装开发依赖

```bash
pip install -e .[dev]
```

### 运行测试

```bash
pytest tests/
```

## 📄 许可证

MIT License

## 👨‍💻 作者

Andy_127【浅醉丶墨语】
- 微信: yangjun_127

## 🤝 贡献

欢迎提交Issue和Pull Request！
