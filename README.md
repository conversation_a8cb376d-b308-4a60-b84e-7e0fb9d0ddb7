# 文件筛选过滤工具 v1.2

一个功能强大的文件筛选和管理工具，支持多种筛选条件和文件操作。

## ✨ 新版本特性 (v1.2)

- 🎨 **Windows风格渐变主题**: 参考Windows 11设计语言的现代化界面
- ✅ **复选框✓符号**: 清晰直观的复选框显示效果
- 🌓 **主题切换功能**: 支持Windows浅色主题和VS深色主题切换 (Ctrl+T)
- 📐 **响应式布局**: 适配各种屏幕尺寸的智能布局设计
- 🔧 **模块化架构**: 重构代码结构，提高维护性和扩展性

## 🚀 功能特性

- 🔍 **多条件筛选**: 支持文件扩展名、名称模式、大小、修改时间等多种筛选条件
- 🔄 **重复文件检测**: 智能检测重复文件，支持自定义重复数量阈值
- 📝 **文件名相似度**: 检测文件名相似的文件，可调节相似度阈值
- 📁 **文件操作**: 支持复制、移动、删除等文件操作
- 💾 **配置管理**: 保存和加载常用的筛选配置
- 🎨 **现代化界面**: 基于PySide6的美观用户界面
- 📊 **详细统计**: 显示搜索结果的详细统计信息
- 📝 **操作日志**: 实时显示操作进度和结果

## 📋 安装要求

- Python 3.7+
- PySide6

## 🛠️ 安装方法

1. 克隆或下载项目
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

## 🎯 使用方法

### 启动程序

```bash
# 普通启动（显示控制台）
python run.py

# 无控制台启动（推荐）
python run_no_console.pyw

# 使用bin目录脚本
./bin/file-filter-tool              # Linux/Mac
bin\file-filter-tool.bat            # Windows
bin\file-filter-tool-no-console.pyw # 无控制台
```

### 基本操作

1. **选择搜索目录**: 点击"浏览"按钮选择要搜索的目录
2. **设置筛选条件**: 根据需要设置文件扩展名、大小、修改时间等条件
3. **高级筛选**: 可选择重复文件检测或文件名相似度检测
4. **执行搜索**: 点击"搜索文件"按钮开始搜索
5. **文件操作**: 对搜索结果进行复制、移动或删除操作
6. **主题切换**: 使用 Ctrl+T 或菜单切换主题

### 快捷键

- `Ctrl+N`: 新建搜索
- `Ctrl+T`: 切换主题
- `Ctrl+Q`: 退出程序
- `Ctrl+Shift+S`: 重复文件统计

## 📁 项目结构

```
file_filter_tool/
├── bin/                           # 可执行脚本
│   ├── file-filter-tool          # Linux/Mac启动脚本
│   ├── file-filter-tool.bat      # Windows启动脚本
│   └── file-filter-tool-no-console.pyw # 无控制台启动
├── src/                           # 源代码目录
│   └── file_filter_tool/         # 主包
│       ├── __init__.py
│       ├── file_filter.py         # 核心筛选逻辑
│       ├── config.py              # 配置管理
│       ├── gui_main.py            # 主窗口 (重构版)
│       ├── ui/                    # UI模块
│       │   ├── __init__.py
│       │   ├── themes.py          # 主题管理
│       │   └── components.py      # UI组件
│       └── core/                  # 核心模块
│           ├── __init__.py
│           └── threads.py         # 线程管理
├── config/                        # 配置文件
│   ├── filter_config.json         # 主配置文件
│   └── sample_config.json         # 示例配置
├── docs/                          # 文档目录
│   ├── Windows主题说明.md         # Windows主题说明
│   ├── 代码重构说明.md            # 重构说明
│   ├── 布局优化说明.md            # 布局优化说明
│   └── ...                       # 其他文档
├── tests/                         # 测试目录
│   ├── test_modules.py            # 模块测试
│   ├── test_themes.py             # 主题测试
│   └── test_layout.py             # 布局测试
├── assets/                        # 资源文件
│   ├── icons/                     # 图标文件
│   └── res/                       # 其他资源
├── requirements.txt               # 依赖列表
├── run.py                         # 主启动脚本
├── run_no_console.pyw             # 无控制台启动脚本
└── README.md                      # 项目说明
```

## 🧪 测试

```bash
# 模块测试
python tests/test_modules.py

# 主题测试
python tests/test_themes.py

# 布局测试
python tests/test_layout.py
```

## 🎨 主题说明

### Windows主题 (默认)
- 浅色渐变背景
- Windows 11风格设计
- 蓝色渐变按钮
- 复选框✓符号显示

### VS主题 (备选)
- 深色专业界面
- Visual Studio风格
- 护眼深色配色
- 适合长时间使用

## 📝 更新日志

### v1.2 (2024年)
- ✨ 新增Windows风格渐变主题
- ✅ 复选框显示✓符号
- 🌓 支持主题切换功能
- 📐 优化响应式布局
- 🔧 重构代码架构
- 📝 完善操作日志

### v1.1
- 🎨 界面美化
- 📊 功能完善
- 🐛 问题修复

## 👨‍💻 开发者信息

- **作者**: Andy_127【浅醉丶墨语】
- **联系方式**: yangjun_127
- **版本**: v1.2
- **更新时间**: 2024年

## 📄 许可证

本项目采用 MIT 许可证。

---

🎉 **享受现代化的文件筛选体验！**
