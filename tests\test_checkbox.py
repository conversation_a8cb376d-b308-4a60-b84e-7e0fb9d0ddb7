#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复选框测试脚本
测试自定义复选框的✓符号显示效果
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox
    )
    from PySide6.QtCore import QTimer
    from file_filter_tool.ui.components import CustomCheckBox
    from file_filter_tool.ui.themes import WindowsTheme, VSTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class CheckBoxTestWidget(QWidget):
    """复选框测试控件"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "windows"
        self.checkboxes = []
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("复选框✓符号测试")
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.theme_btn = QPushButton("切换到 VS 主题")
        self.theme_btn.clicked.connect(self.switch_theme)
        theme_layout.addWidget(self.theme_btn)
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 复选框测试组
        checkbox_group = QGroupBox("复选框✓符号测试")
        checkbox_layout = QVBoxLayout(checkbox_group)
        
        # 创建测试复选框
        checkbox1 = CustomCheckBox("选项1 - 未选中")
        checkbox2 = CustomCheckBox("选项2 - 已选中")
        checkbox2.setChecked(True)
        checkbox3 = CustomCheckBox("选项3 - 递归搜索子目录")
        checkbox4 = CustomCheckBox("选项4 - 仅显示重复文件")
        checkbox5 = CustomCheckBox("选项5 - 仅显示相似文件名")
        checkbox6 = CustomCheckBox("选项6 - 禁用状态")
        checkbox6.setEnabled(False)
        
        self.checkboxes = [checkbox1, checkbox2, checkbox3, checkbox4, checkbox5, checkbox6]
        
        for checkbox in self.checkboxes:
            checkbox_layout.addWidget(checkbox)
        
        layout.addWidget(checkbox_group)
        
        # 说明文字
        info_label = QLabel("""
测试说明:
1. 点击复选框查看✓符号显示效果
2. 切换主题查看不同主题下的显示效果
3. 悬停在复选框上查看悬停效果
4. 已选中的复选框应该显示清晰的✓符号
        """)
        layout.addWidget(info_label)
        
        layout.addStretch()
    
    def apply_theme(self):
        """应用主题"""
        if self.current_theme == "windows":
            self.setStyleSheet(WindowsTheme.get_complete_style())
            # 更新复选框样式
            for checkbox in self.checkboxes:
                checkbox.apply_windows_style()
        else:
            self.setStyleSheet(VSTheme.get_complete_style())
            # 更新复选框样式
            for checkbox in self.checkboxes:
                checkbox.apply_vs_style()
    
    def switch_theme(self):
        """切换主题"""
        if self.current_theme == "windows":
            self.current_theme = "vs"
            self.theme_btn.setText("切换到 Windows 主题")
            print("🌙 切换到 Visual Studio 深色主题")
        else:
            self.current_theme = "windows"
            self.theme_btn.setText("切换到 VS 主题")
            print("☀️ 切换到 Windows 浅色主题")
        
        self.apply_theme()

def test_checkbox():
    """测试复选框"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行复选框测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("✅ 复选框✓符号测试启动")
    print("=" * 40)
    print("📋 测试内容:")
    print("  • 自定义复选框✓符号显示")
    print("  • Windows主题下的复选框样式")
    print("  • VS主题下的复选框样式")
    print("  • 复选框悬停效果")
    print("  • 主题切换时复选框样式更新")
    print()
    print("💡 使用说明:")
    print("  • 点击复选框查看✓符号")
    print("  • 点击 '切换主题' 按钮测试不同主题")
    print("  • 悬停在复选框上查看效果")
    print("  • 观察已选中复选框的✓符号显示")
    print()
    
    # 创建测试窗口
    test_widget = CheckBoxTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行复选框测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_checkbox()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
