# 新功能说明

## 🆕 新增功能概览

### 1. 🔇 隐藏命令行窗口
- **功能描述**: 运行程序时不显示黑色的命令行窗口
- **实现方式**: 创建了 `.pyw` 扩展名的启动文件
- **使用方法**: 双击 `启动GUI无窗口.pyw` 文件启动

### 2. 🔄 重复文件检测与筛选
- **功能描述**: 检测相同内容的文件并按数量进行筛选
- **检测原理**: 基于文件大小预筛选 + MD5哈希值精确匹配
- **筛选条件**: 可设置最少重复文件数量（默认2个）

---

## 🔇 隐藏命令行窗口功能

### 📁 新增文件
- **`启动GUI无窗口.pyw`** - 无窗口启动器

### 🎯 功能特点
1. **完全隐藏命令行**: 使用 `.pyw` 扩展名，Windows系统会用 `pythonw.exe` 运行
2. **智能错误处理**: 如果启动失败，会显示图形化错误对话框
3. **自动选择界面**: 优先启动PySide6界面，备用tkinter界面
4. **错误日志记录**: 启动失败时会生成 `error.log` 文件

### 🚀 使用方式
```bash
# 方式1：双击文件（推荐）
双击 启动GUI无窗口.pyw

# 方式2：命令行启动
python 启动GUI无窗口.pyw

# 方式3：传统方式（会显示命令行）
python 启动GUI.py
```

### 💡 技术原理
- **`.pyw` 扩展名**: Windows系统自动使用 `pythonw.exe` 运行
- **`pythonw.exe`**: Python的无窗口版本，不会显示控制台窗口
- **错误处理**: 使用tkinter显示错误对话框，确保用户能看到错误信息

---

## 🔄 重复文件检测功能

### 🎛️ 界面控件
在筛选条件区域新增：
- **"仅显示重复文件"** 复选框
- **"最少重复"** 数量选择器（2-999个）

### 🔍 检测原理

#### 第一步：文件大小预筛选
```python
# 按文件大小分组，快速排除不可能重复的文件
size_groups = defaultdict(list)
for file_path in files:
    size = file_path.stat().st_size
    size_groups[size].append(file_path)
```

#### 第二步：MD5哈希值精确匹配
```python
# 对相同大小的文件计算MD5哈希值
hash_groups = defaultdict(list)
for size, file_list in size_groups.items():
    if len(file_list) >= min_count:
        for file_path in file_list:
            file_hash = calculate_md5(file_path)
            hash_groups[file_hash].append(file_path)
```

#### 第三步：重复文件分组
```python
# 收集真正重复的文件
duplicate_files = []
for file_hash, file_list in hash_groups.items():
    if len(file_list) >= min_count:
        duplicate_files.extend(file_list)
```

### 📊 结果显示

#### 表格新增列
- **"重复状态"** 列：显示重复文件的分组信息
- **格式**: `重复组 ABC123 (3个)` 
  - `ABC123`: 基于MD5哈希值的前6位生成的组ID
  - `(3个)`: 该组中重复文件的数量

#### 状态栏信息
- **普通搜索**: `找到 15 个文件`
- **重复文件搜索**: `找到 8 个重复文件，共 3 组`

### 🎯 使用场景

#### 1. 清理重复文件
```
设置条件：
✅ 仅显示重复文件
📁 搜索路径：D:\Downloads
🔢 最少重复：2个

结果：找到所有重复的下载文件
```

#### 2. 查找大量重复
```
设置条件：
✅ 仅显示重复文件
🔢 最少重复：5个
📏 文件大小：> 10MB

结果：找到被大量复制的大文件
```

#### 3. 特定类型重复文件
```
设置条件：
✅ 仅显示重复文件
📄 文件扩展名：.jpg .png
🔢 最少重复：3个

结果：找到重复的图片文件
```

### ⚡ 性能优化

#### 分层检测策略
1. **文件大小预筛选**: 快速排除99%不可能重复的文件
2. **哈希值计算**: 只对可能重复的文件计算MD5
3. **分块读取**: 大文件分块计算哈希，避免内存溢出

#### 进度提示
```
正在搜索文件...
正在检测重复文件... (共1250个文件)
```

### 🔧 技术细节

#### MD5哈希计算
```python
def _calculate_file_hash(self, file_path: Path, chunk_size: int = 8192) -> str:
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(chunk_size), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except (OSError, IOError):
        return ""
```

#### 重复文件分组
```python
def get_duplicate_groups(self, files: List[Path]) -> Dict[str, List[Path]]:
    """获取重复文件分组信息"""
    # 返回格式: {hash: [file1, file2, file3]}
    # 只返回真正重复的分组（文件数量 >= 2）
```

---

## 🎉 使用建议

### 🔇 隐藏命令行窗口
1. **日常使用**: 双击 `启动GUI无窗口.pyw` 获得最佳体验
2. **调试模式**: 使用 `python gui_pyside6.py` 查看调试信息
3. **兼容性**: 如果.pyw有问题，使用传统的 `启动GUI.py`

### 🔄 重复文件检测
1. **大目录搜索**: 建议先设置文件类型或大小范围
2. **性能考虑**: 重复文件检测比普通搜索慢，请耐心等待
3. **结果分析**: 通过"重复组"信息快速识别相同文件
4. **批量操作**: 可以对重复文件进行批量删除或移动

### 💡 最佳实践
1. **定期清理**: 定期检查Downloads、Desktop等目录的重复文件
2. **备份前检查**: 备份前先清理重复文件，节省存储空间
3. **分类整理**: 利用重复文件检测功能整理照片、文档等
4. **配置保存**: 将常用的重复文件检测条件保存为配置

---

## 🔮 技术优势

### 🔇 隐藏命令行
- ✅ **用户体验**: 专业软件般的启动体验
- ✅ **系统集成**: 可以创建桌面快捷方式
- ✅ **错误处理**: 完善的图形化错误提示

### 🔄 重复文件检测
- ✅ **准确性**: MD5哈希确保100%准确匹配
- ✅ **性能**: 分层检测策略，大幅提升检测速度
- ✅ **可视化**: 直观的分组显示，便于理解和操作
- ✅ **灵活性**: 可配置最少重复数量，适应不同需求

这两个新功能大大提升了软件的专业性和实用性，让文件管理变得更加高效和便捷！
