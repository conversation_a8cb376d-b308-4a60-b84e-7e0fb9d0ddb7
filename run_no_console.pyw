#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选过滤工具 - 无控制台启动脚本
File Filter Tool - No Console Launcher Script

版本: v1.2
作者: Andy_127【浅醉丶墨语】
联系: yangjun_127

特性:
- Windows风格渐变主题
- 复选框✓符号显示
- 主题切换功能 (Ctrl+T)
- 响应式布局设计
- 无控制台窗口启动
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def show_error(message):
    """显示错误信息"""
    # 写入错误日志
    try:
        with open("error.log", "w", encoding="utf-8") as f:
            f.write(f"启动错误: {message}\n")
    except:
        pass

    # 尝试使用系统通知
    try:
        import subprocess
        import sys
        if sys.platform == "win32":
            # Windows系统使用msg命令
            subprocess.run(["msg", "*", f"文件筛选工具启动错误:\n{message}"],
                         capture_output=True, timeout=5)
    except:
        pass

def main():
    """主函数"""
    try:
        # 启动重构后的PySide6版本
        from file_filter_tool.gui_main import main as gui_main
        gui_main()
    except ImportError as e:
        show_error(f"无法导入PySide6模块\n请确保已安装PySide6：pip install PySide6\n\n详细错误：{e}")
        return False
    except Exception as e:
        show_error(f"启动失败：{e}")
        return False

    return True

if __name__ == '__main__':
    main()
