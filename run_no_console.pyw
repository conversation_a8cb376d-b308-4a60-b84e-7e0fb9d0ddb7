#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 无窗口启动脚本
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def show_error(message):
    """显示错误对话框"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动错误", message)
        root.destroy()
    except:
        # 如果连tkinter都不可用，写入错误日志
        try:
            with open("error.log", "w", encoding="utf-8") as f:
                f.write(f"启动错误: {message}\n")
        except:
            pass

def main():
    """主函数"""
    try:
        # 尝试启动PySide6版本
        from file_filter_tool.gui_pyside6 import main as pyside6_main
        pyside6_main()
    except ImportError:
        try:
            # 备用tkinter版本
            from file_filter_tool.gui import main as tkinter_main
            tkinter_main()
        except ImportError:
            show_error("无法导入GUI模块\n请确保已安装PySide6：pip install PySide6")
            return False
    except Exception as e:
        show_error(f"启动失败：{e}")
        return False
    
    return True

if __name__ == '__main__':
    main()
