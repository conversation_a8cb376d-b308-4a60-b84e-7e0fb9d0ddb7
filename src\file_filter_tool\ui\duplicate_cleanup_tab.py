#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复文件清理标签页
专门用于查找和清理重复文件
"""

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
        QGroupBox, QPushButton, QTableWidget, QTableWidgetItem,
        QHeaderView, QProgressBar, QTextEdit, QLabel,
        QComboBox, QCheckBox, QSpinBox, QSlider,
        QFrame, QTreeWidget, QTreeWidgetItem
    )
    from PySide6.QtCore import Qt, Signal
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

from .components import SearchPathWidget


class DuplicateCleanupTab(QWidget):
    """重复文件清理标签页"""
    
    # 信号定义
    status_message = Signal(str)
    progress_update = Signal(int, int, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.duplicate_groups = []
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：搜索设置
        left_widget = self.create_search_settings_widget()
        main_splitter.addWidget(left_widget)
        
        # 右侧：重复文件显示
        right_widget = self.create_duplicate_display_widget()
        main_splitter.addWidget(right_widget)
        
        # 设置分割器比例
        main_splitter.setSizes([350, 850])
        layout.addWidget(main_splitter)
        
        # 底部：操作日志
        bottom_widget = self.create_bottom_widget()
        layout.addWidget(bottom_widget)
    
    def create_search_settings_widget(self):
        """创建搜索设置控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 搜索路径
        path_group = QGroupBox("📁 搜索路径")
        path_layout = QVBoxLayout(path_group)
        self.search_path_widget = SearchPathWidget()
        path_layout.addWidget(self.search_path_widget)
        layout.addWidget(path_group)
        
        # 重复检测设置
        detection_group = QGroupBox("🔍 重复检测设置")
        detection_layout = QVBoxLayout(detection_group)
        
        # 检测方式
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("检测方式:"))
        self.detection_method_combo = QComboBox()
        self.detection_method_combo.addItems([
            "文件大小 + MD5",
            "仅文件大小",
            "仅文件名",
            "文件名 + 大小",
            "内容相似度"
        ])
        method_layout.addWidget(self.detection_method_combo)
        detection_layout.addLayout(method_layout)
        
        # 最小文件大小
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("最小文件大小:"))
        self.min_size_spin = QSpinBox()
        self.min_size_spin.setRange(0, 999999)
        self.min_size_spin.setValue(1)
        self.min_size_spin.setSuffix(" KB")
        size_layout.addWidget(self.min_size_spin)
        size_layout.addStretch()
        detection_layout.addLayout(size_layout)
        
        # 最少重复数量
        count_layout = QHBoxLayout()
        count_layout.addWidget(QLabel("最少重复数量:"))
        self.min_duplicate_spin = QSpinBox()
        self.min_duplicate_spin.setRange(2, 100)
        self.min_duplicate_spin.setValue(2)
        self.min_duplicate_spin.setSuffix(" 个")
        count_layout.addWidget(self.min_duplicate_spin)
        count_layout.addStretch()
        detection_layout.addLayout(count_layout)
        
        layout.addWidget(detection_group)
        
        # 筛选选项
        filter_group = QGroupBox("🎯 筛选选项")
        filter_layout = QVBoxLayout(filter_group)
        
        self.include_hidden_check = QCheckBox("包含隐藏文件")
        filter_layout.addWidget(self.include_hidden_check)
        
        self.include_system_check = QCheckBox("包含系统文件")
        filter_layout.addWidget(self.include_system_check)
        
        self.recursive_check = QCheckBox("递归搜索子目录")
        self.recursive_check.setChecked(True)
        filter_layout.addWidget(self.recursive_check)
        
        # 文件类型筛选
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("文件类型:"))
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems([
            "所有文件",
            "图片文件",
            "视频文件",
            "音频文件",
            "文档文件",
            "自定义"
        ])
        type_layout.addWidget(self.file_type_combo)
        filter_layout.addLayout(type_layout)
        
        layout.addWidget(filter_group)
        
        # 清理策略
        strategy_group = QGroupBox("🧹 清理策略")
        strategy_layout = QVBoxLayout(strategy_group)
        
        self.strategy_combo = QComboBox()
        self.strategy_combo.addItems([
            "手动选择",
            "保留最新文件",
            "保留最旧文件",
            "保留最大文件",
            "保留最小文件",
            "保留路径最短",
            "智能推荐"
        ])
        strategy_layout.addWidget(self.strategy_combo)
        
        # 安全选项
        self.safe_mode_check = QCheckBox("安全模式（移动到回收站）")
        self.safe_mode_check.setChecked(True)
        strategy_layout.addWidget(self.safe_mode_check)
        
        self.backup_before_delete_check = QCheckBox("删除前备份")
        strategy_layout.addWidget(self.backup_before_delete_check)
        
        layout.addWidget(strategy_group)
        
        # 操作按钮
        btn_layout = QVBoxLayout()
        
        self.scan_btn = QPushButton("🔍 开始扫描")
        self.scan_btn.setMinimumHeight(35)
        btn_layout.addWidget(self.scan_btn)
        
        self.preview_btn = QPushButton("👁️ 预览清理")
        self.preview_btn.setMinimumHeight(35)
        self.preview_btn.setEnabled(False)
        btn_layout.addWidget(self.preview_btn)
        
        self.execute_btn = QPushButton("🗑️ 执行清理")
        self.execute_btn.setMinimumHeight(35)
        self.execute_btn.setEnabled(False)
        btn_layout.addWidget(self.execute_btn)
        
        layout.addLayout(btn_layout)
        layout.addStretch()
        
        return widget
    
    def create_duplicate_display_widget(self):
        """创建重复文件显示控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 统计信息
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        stats_frame.setMaximumHeight(60)
        
        stats_layout = QHBoxLayout(stats_frame)
        stats_layout.setContentsMargins(15, 10, 15, 10)
        
        self.stats_label = QLabel("📊 等待扫描...")
        self.stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        stats_layout.addWidget(self.stats_label)
        
        stats_layout.addStretch()
        
        # 快速操作按钮
        self.select_all_btn = QPushButton("📋 全选")
        self.select_all_btn.setMaximumWidth(80)
        self.select_all_btn.setEnabled(False)
        stats_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("❌ 全不选")
        self.select_none_btn.setMaximumWidth(80)
        self.select_none_btn.setEnabled(False)
        stats_layout.addWidget(self.select_none_btn)
        
        self.auto_select_btn = QPushButton("🤖 智能选择")
        self.auto_select_btn.setMaximumWidth(100)
        self.auto_select_btn.setEnabled(False)
        stats_layout.addWidget(self.auto_select_btn)
        
        layout.addWidget(stats_frame)
        
        # 重复文件树形显示
        self.duplicate_tree = QTreeWidget()
        self.duplicate_tree.setHeaderLabels([
            "文件名", "大小", "修改时间", "路径", "操作"
        ])
        self.duplicate_tree.setAlternatingRowColors(True)
        self.duplicate_tree.setRootIsDecorated(True)
        
        # 设置列宽
        header = self.duplicate_tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 文件名
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 大小
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 时间
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)           # 路径
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 操作
        
        layout.addWidget(self.duplicate_tree)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        self.keep_newest_btn = QPushButton("📅 保留最新")
        self.keep_newest_btn.setEnabled(False)
        batch_layout.addWidget(self.keep_newest_btn)
        
        self.keep_largest_btn = QPushButton("📏 保留最大")
        self.keep_largest_btn.setEnabled(False)
        batch_layout.addWidget(self.keep_largest_btn)
        
        self.keep_shortest_path_btn = QPushButton("📍 保留最短路径")
        self.keep_shortest_path_btn.setEnabled(False)
        batch_layout.addWidget(self.keep_shortest_path_btn)
        
        batch_layout.addStretch()
        
        self.export_list_btn = QPushButton("📋 导出列表")
        self.export_list_btn.setEnabled(False)
        batch_layout.addWidget(self.export_list_btn)
        
        layout.addLayout(batch_layout)
        
        return widget
    
    def create_bottom_widget(self):
        """创建底部控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        log_group = QGroupBox("📝 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return widget
    
    def connect_signals(self):
        """连接信号"""
        # 主要操作按钮
        self.scan_btn.clicked.connect(self.start_scan)
        self.preview_btn.clicked.connect(self.preview_cleanup)
        self.execute_btn.clicked.connect(self.execute_cleanup)
        
        # 快速操作按钮
        self.select_all_btn.clicked.connect(self.select_all_duplicates)
        self.select_none_btn.clicked.connect(self.select_no_duplicates)
        self.auto_select_btn.clicked.connect(self.auto_select_duplicates)
        
        # 批量操作按钮
        self.keep_newest_btn.clicked.connect(self.keep_newest_files)
        self.keep_largest_btn.clicked.connect(self.keep_largest_files)
        self.keep_shortest_path_btn.clicked.connect(self.keep_shortest_path_files)
        self.export_list_btn.clicked.connect(self.export_duplicate_list)
        
        # 设置变化信号
        self.detection_method_combo.currentTextChanged.connect(self.on_detection_method_changed)
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
    
    def start_scan(self):
        """开始扫描重复文件"""
        self.log_message("🔍 开始扫描重复文件...")
        self.status_message.emit("正在扫描重复文件...")
        
        # 获取搜索设置
        search_path = self.search_path_widget.get_path()
        if not search_path:
            self.log_message("❌ 请选择搜索路径")
            return
        
        # 这里应该调用实际的重复文件扫描逻辑
        self.simulate_duplicate_scan()
    
    def simulate_duplicate_scan(self):
        """模拟重复文件扫描"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)
        
        # 模拟扫描进度
        import time
        for i in range(101):
            self.progress_bar.setValue(i)
            self.progress_update.emit(i, 100, f"扫描进度: {i}%")
            time.sleep(0.01)
        
        # 模拟扫描结果
        self.duplicate_groups = [
            {
                "name": "document.pdf",
                "files": [
                    {"path": "/path1/document.pdf", "size": "1.2 MB", "mtime": "2024-01-15 10:30"},
                    {"path": "/path2/document.pdf", "size": "1.2 MB", "mtime": "2024-01-14 15:20"},
                ]
            },
            {
                "name": "image.jpg",
                "files": [
                    {"path": "/photos/image.jpg", "size": "2.5 MB", "mtime": "2024-01-10 09:15"},
                    {"path": "/backup/image.jpg", "size": "2.5 MB", "mtime": "2024-01-10 09:15"},
                    {"path": "/temp/image.jpg", "size": "2.5 MB", "mtime": "2024-01-10 09:15"},
                ]
            }
        ]
        
        self.display_duplicate_results()
        self.progress_bar.setVisible(False)
        
        # 启用相关按钮
        self.preview_btn.setEnabled(True)
        self.execute_btn.setEnabled(True)
        self.select_all_btn.setEnabled(True)
        self.select_none_btn.setEnabled(True)
        self.auto_select_btn.setEnabled(True)
        self.keep_newest_btn.setEnabled(True)
        self.keep_largest_btn.setEnabled(True)
        self.keep_shortest_path_btn.setEnabled(True)
        self.export_list_btn.setEnabled(True)
        
        self.log_message(f"✅ 扫描完成，发现 {len(self.duplicate_groups)} 组重复文件")
        self.status_message.emit(f"发现 {len(self.duplicate_groups)} 组重复文件")
    
    def display_duplicate_results(self):
        """显示重复文件结果"""
        self.duplicate_tree.clear()
        
        total_files = 0
        total_waste = 0
        
        for group in self.duplicate_groups:
            # 创建组节点
            group_item = QTreeWidgetItem(self.duplicate_tree)
            group_item.setText(0, f"📁 {group['name']} ({len(group['files'])} 个副本)")
            group_item.setExpanded(True)
            
            # 添加文件节点
            for file_info in group['files']:
                file_item = QTreeWidgetItem(group_item)
                file_item.setText(0, group['name'])
                file_item.setText(1, file_info['size'])
                file_item.setText(2, file_info['mtime'])
                file_item.setText(3, file_info['path'])
                file_item.setText(4, "保留")  # 默认操作
                
                total_files += 1
        
        # 更新统计信息
        self.stats_label.setText(
            f"📊 发现 {len(self.duplicate_groups)} 组重复文件，"
            f"共 {total_files} 个文件"
        )
    
    def preview_cleanup(self):
        """预览清理操作"""
        self.log_message("👁️ 预览清理操作...")
        self.status_message.emit("正在预览清理操作")
    
    def execute_cleanup(self):
        """执行清理操作"""
        self.log_message("🗑️ 开始执行清理操作...")
        self.status_message.emit("正在执行清理操作")
    
    def select_all_duplicates(self):
        """全选重复文件"""
        self.log_message("📋 已全选所有重复文件")
    
    def select_no_duplicates(self):
        """全不选重复文件"""
        self.log_message("❌ 已取消选择所有重复文件")
    
    def auto_select_duplicates(self):
        """智能选择重复文件"""
        self.log_message("🤖 智能选择重复文件...")
    
    def keep_newest_files(self):
        """保留最新文件"""
        self.log_message("📅 设置保留最新文件")
    
    def keep_largest_files(self):
        """保留最大文件"""
        self.log_message("📏 设置保留最大文件")
    
    def keep_shortest_path_files(self):
        """保留最短路径文件"""
        self.log_message("📍 设置保留最短路径文件")
    
    def export_duplicate_list(self):
        """导出重复文件列表"""
        self.log_message("📋 导出重复文件列表...")
    
    def on_detection_method_changed(self, method):
        """检测方式变化处理"""
        self.log_message(f"🔍 检测方式已更改: {method}")
    
    def on_strategy_changed(self, strategy):
        """清理策略变化处理"""
        self.log_message(f"🧹 清理策略已更改: {strategy}")
    
    def log_message(self, message):
        """记录日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        self.log_text.ensureCursorVisible()
    
    def get_settings(self):
        """获取当前设置"""
        return {
            "search_path": self.search_path_widget.get_path(),
            "detection_method": self.detection_method_combo.currentText(),
            "min_size": self.min_size_spin.value(),
            "min_duplicate": self.min_duplicate_spin.value(),
            "strategy": self.strategy_combo.currentText(),
            "safe_mode": self.safe_mode_check.isChecked(),
            "backup_before_delete": self.backup_before_delete_check.isChecked()
        }
    
    def load_settings(self, settings):
        """加载设置"""
        if "search_path" in settings:
            self.search_path_widget.set_path(settings["search_path"])
        # 可以继续加载其他设置...
    
    def clear_settings(self):
        """清空设置"""
        self.duplicate_tree.clear()
        self.duplicate_groups.clear()
        self.stats_label.setText("📊 等待扫描...")
        self.log_message("🔄 已清空所有设置")
