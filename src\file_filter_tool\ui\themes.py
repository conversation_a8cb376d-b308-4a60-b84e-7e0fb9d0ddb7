"""
主题管理模块
提供各种UI主题的样式定义
"""

class WindowsTheme:
    """Windows 风格渐变主题"""

    # 基础颜色定义 - 参考Windows 11主题
    COLORS = {
        'background': '#f3f3f3',
        'surface': '#ffffff',
        'surface_variant': '#fafafa',
        'surface_bright': '#ffffff',
        'border': '#d1d1d1',
        'border_light': '#e5e5e5',
        'border_dark': '#adadad',
        'primary': '#0078d4',
        'primary_hover': '#106ebe',
        'primary_pressed': '#005a9e',
        'primary_light': '#deecf9',
        'text_primary': '#323130',
        'text_secondary': '#605e5c',
        'text_accent': '#0078d4',
        'text_disabled': '#a19f9d',
        'success': '#107c10',
        'warning': '#ff8c00',
        'error': '#d13438',
        'selection': '#0078d4',
        'shadow': 'rgba(0, 0, 0, 0.1)',
        'shadow_dark': 'rgba(0, 0, 0, 0.2)',
    }

    @classmethod
    def get_main_style(cls):
        """获取主要样式表"""
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['surface']},
                stop: 1 {cls.COLORS['surface_variant']});
            color: {cls.COLORS['text_primary']};
        }}

        /* 中央部件 */
        QWidget {{
            background-color: {cls.COLORS['background']};
            color: {cls.COLORS['text_primary']};
        }}

        /* 标题栏样式 */
        QMainWindow::title {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['text_primary']};
        }}
        """

    @classmethod
    def get_menu_style(cls):
        """获取菜单样式表"""
        return f"""
        /* 菜单栏样式 */
        QMenuBar {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['surface']},
                stop: 1 {cls.COLORS['surface_variant']});
            color: {cls.COLORS['text_primary']};
            border-bottom: 1px solid {cls.COLORS['border']};
            padding: 2px;
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 6px 12px;
            border-radius: 4px;
        }}

        QMenuBar::item:selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_light']},
                stop: 1 {cls.COLORS['border_light']});
            border: 1px solid {cls.COLORS['border']};
        }}

        QMenuBar::item:pressed {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            color: white;
        }}

        /* 菜单样式 */
        QMenu {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['text_primary']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 4px;
        }}

        QMenu::item {{
            padding: 6px 20px;
            border-radius: 3px;
        }}

        QMenu::item:selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_light']},
                stop: 1 {cls.COLORS['border_light']});
        }}

        QMenu::separator {{
            height: 1px;
            background-color: {cls.COLORS['border']};
            margin: 2px 0px;
        }}
        """

    @classmethod
    def get_input_style(cls):
        """获取输入控件样式表"""
        return f"""
        /* 输入框样式 */
        QLineEdit {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 6px 8px;
            color: {cls.COLORS['text_primary']};
            selection-background-color: {cls.COLORS['selection']};
            selection-color: white;
        }}

        QLineEdit:focus {{
            border: 2px solid {cls.COLORS['primary']};
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['surface']},
                stop: 1 {cls.COLORS['surface_variant']});
        }}

        QLineEdit:hover {{
            border: 2px solid {cls.COLORS['primary_hover']};
        }}

        /* 数字输入框样式 - 上下按钮布局 */
        QSpinBox {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 4px 6px;
            color: {cls.COLORS['text_primary']};
            selection-background-color: {cls.COLORS['selection']};
            selection-color: white;
            min-height: 20px;
        }}

        QSpinBox:focus {{
            border: 2px solid {cls.COLORS['primary']};
        }}

        QSpinBox:hover {{
            border: 2px solid {cls.COLORS['primary_hover']};
        }}

        /* SpinBox按钮 - 上下布局，黑色箭头 */
        QSpinBox::up-button {{
            subcontrol-origin: border;
            subcontrol-position: top right;
            width: 20px;
            height: 12px;
        }}

        QSpinBox::down-button {{
            subcontrol-origin: border;
            subcontrol-position: bottom right;
            width: 20px;
            height: 12px;
        }}

        QSpinBox::up-arrow {{
            image: none;
            width: 0px;
            height: 0px;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 6px solid black;
        }}

        QSpinBox::down-arrow {{
            image: none;
            width: 0px;
            height: 0px;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid black;
        }}

        /* 下拉框样式 */
        QComboBox {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 4px 6px;
            color: {cls.COLORS['text_primary']};
            selection-background-color: {cls.COLORS['selection']};
            selection-color: white;
            min-height: 20px;
        }}

        QComboBox:focus {{
            border: 2px solid {cls.COLORS['primary']};
        }}

        QComboBox:hover {{
            border: 2px solid {cls.COLORS['primary_hover']};
        }}



        /* ComboBox下拉箭头 - 黑色箭头 */
        QComboBox::down-arrow {{
            image: none;
            width: 0px;
            height: 0px;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid black;
        }}

        QComboBox QAbstractItemView {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            selection-background-color: {cls.COLORS['primary']};
            selection-color: white;
            color: {cls.COLORS['text_primary']};
        }}

        /* 日期选择器样式 */
        QDateEdit {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            padding: 4px 6px;
            color: {cls.COLORS['text_primary']};
            selection-background-color: {cls.COLORS['selection']};
            selection-color: white;
            min-height: 20px;
        }}

        QDateEdit:focus {{
            border: 2px solid {cls.COLORS['primary']};
        }}

        QDateEdit:hover {{
            border: 2px solid {cls.COLORS['primary_hover']};
        }}

        /* DateEdit下拉箭头 - 黑色箭头 */
        QDateEdit::down-arrow {{
            image: none;
            width: 0px;
            height: 0px;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 6px solid black;
        }}


        """

    @classmethod
    def get_button_style(cls):
        """获取按钮样式表"""
        return f"""
        /* 按钮样式 */
        QPushButton {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            border: 1px solid {cls.COLORS['primary']};
            border-radius: 4px;
            padding: 8px 16px;
            color: white;
            font-weight: bold;
        }}

        QPushButton:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_hover']},
                stop: 1 {cls.COLORS['primary']});
            border: 1px solid {cls.COLORS['primary_hover']};
        }}

        QPushButton:pressed {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_pressed']},
                stop: 1 {cls.COLORS['primary']});
            border: 1px solid {cls.COLORS['primary_pressed']};
        }}

        QPushButton:disabled {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['border_light']},
                stop: 1 {cls.COLORS['border']});
            color: {cls.COLORS['text_disabled']};
            border: 1px solid {cls.COLORS['border']};
        }}
        """

    @classmethod
    def get_checkbox_style(cls):
        """获取复选框样式表 - 带✓符号"""
        return f"""
        /* 复选框样式 */
        QCheckBox {{
            color: {cls.COLORS['text_primary']};
            spacing: 8px;
            font-weight: normal;
        }}

        QCheckBox::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['surface']},
                stop: 1 {cls.COLORS['surface_variant']});
        }}

        QCheckBox::indicator:hover {{
            border: 2px solid {cls.COLORS['primary']};
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_light']},
                stop: 1 {cls.COLORS['surface']});
        }}

        QCheckBox::indicator:checked {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            border: 2px solid {cls.COLORS['primary']};
        }}

        QCheckBox::indicator:checked {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            border: 2px solid {cls.COLORS['primary']};
        }}

        QCheckBox::indicator:checked:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_hover']},
                stop: 1 {cls.COLORS['primary']});
            border: 2px solid {cls.COLORS['primary_hover']};
        }}

        QCheckBox::indicator:disabled {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['border_light']},
                stop: 1 {cls.COLORS['border']});
            border: 2px solid {cls.COLORS['border']};
        }}
        """

    @classmethod
    def get_groupbox_style(cls):
        """获取分组框样式表"""
        return f"""
        /* 分组框样式 */
        QGroupBox {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['surface']},
                stop: 1 {cls.COLORS['surface_variant']});
            border: 2px solid {cls.COLORS['border']};
            border-radius: 6px;
            margin-top: 12px;
            padding-top: 12px;
            font-weight: bold;
            color: {cls.COLORS['text_primary']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            color: {cls.COLORS['text_primary']};
            background-color: {cls.COLORS['surface']};
            font-weight: bold;
            border: 1px solid {cls.COLORS['border']};
            border-radius: 3px;
        }}
        """

    @classmethod
    def get_table_style(cls):
        """获取表格样式表"""
        return f"""
        /* 表格样式 */
        QTableWidget {{
            background-color: {cls.COLORS['surface']};
            alternate-background-color: {cls.COLORS['surface_variant']};
            color: {cls.COLORS['text_primary']};
            gridline-color: {cls.COLORS['border_light']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            selection-background-color: {cls.COLORS['selection']};
            selection-color: white;
        }}

        QTableWidget::item {{
            padding: 8px;
            border: none;
        }}

        QTableWidget::item:selected {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            color: white;
        }}

        QHeaderView::section {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['surface']},
                stop: 1 {cls.COLORS['surface_variant']});
            color: {cls.COLORS['text_primary']};
            padding: 8px;
            border: 1px solid {cls.COLORS['border']};
            border-radius: 0px;
            font-weight: bold;
        }}

        QHeaderView::section:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_light']},
                stop: 1 {cls.COLORS['border_light']});
        }}
        """

    @classmethod
    def get_additional_styles(cls):
        """获取其他样式"""
        return f"""
        /* 状态栏样式 */
        QStatusBar {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            color: white;
            border: none;
            padding: 4px;
            border-top: 1px solid {cls.COLORS['border']};
        }}

        /* 进度条样式 */
        QProgressBar {{
            background-color: {cls.COLORS['surface_variant']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            text-align: center;
            color: {cls.COLORS['text_primary']};
            font-weight: bold;
        }}

        QProgressBar::chunk {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
            border-radius: 2px;
        }}

        /* 标签样式 */
        QLabel {{
            color: {cls.COLORS['text_primary']};
        }}

        /* 分割器样式 */
        QSplitter::handle {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['border_light']},
                stop: 1 {cls.COLORS['border']});
        }}

        QSplitter::handle:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 {cls.COLORS['primary_light']},
                stop: 1 {cls.COLORS['primary']});
        }}

        /* 滚动条样式 */
        QScrollBar:vertical {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 {cls.COLORS['surface_variant']},
                stop: 1 {cls.COLORS['surface']});
            width: 14px;
            border: 1px solid {cls.COLORS['border']};
            border-radius: 7px;
        }}

        QScrollBar::handle:vertical {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 {cls.COLORS['border']},
                stop: 1 {cls.COLORS['border_dark']});
            border-radius: 6px;
            min-height: 20px;
            margin: 1px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                stop: 0 {cls.COLORS['primary']},
                stop: 1 {cls.COLORS['primary_pressed']});
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            border: none;
            background: none;
        }}

        /* 文本编辑器样式（日志区域） */
        QTextEdit {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['text_primary']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 9pt;
            selection-background-color: {cls.COLORS['selection']};
            selection-color: white;
        }}

        QTextEdit:focus {{
            border: 2px solid {cls.COLORS['primary']};
        }}
        """

    @classmethod
    def get_complete_style(cls):
        """获取完整样式表"""
        return (
            cls.get_main_style() +
            cls.get_menu_style() +
            cls.get_input_style() +
            cls.get_button_style() +
            cls.get_checkbox_style() +
            cls.get_groupbox_style() +
            cls.get_table_style() +
            cls.get_additional_styles()
        )


