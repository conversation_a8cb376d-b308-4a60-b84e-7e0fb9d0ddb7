#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化布局测试
测试重新组织后的UI布局和功能
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QTextEdit, QSplitter
    )
    from PySide6.QtCore import Qt
    from file_filter_tool.ui.components import (
        FilterConditionsWidget, AdvancedFilterWidget, OperationButtonsWidget
    )
    from file_filter_tool.ui.themes import WindowsTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class OptimizedLayoutTestWidget(QWidget):
    """优化布局测试控件"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("优化布局测试 - 重新组织的UI布局")
        self.setMinimumSize(1200, 800)
        
        layout = QVBoxLayout(self)
        
        # 说明文字
        info_label = QLabel("""
🎨 优化布局测试

本测试验证重新组织后的UI布局：

✅ 布局调整：
• 文件筛选条件：扩展名、模式、大小、时间、重复文件、相似文件
• 高级操作：双目录对比
• 智能按键：根据模式自动调整功能

✅ 逻辑分组：
• 筛选条件：回答"我要什么样的文件？"
• 高级操作：回答"我要怎样处理文件？"

✅ 用户体验：
• 常用功能集中在筛选条件中
• 特殊功能突出在高级操作中
• 界面更平衡，减少滚动

🎯 测试重点：
• 验证重复文件和相似文件筛选已移到筛选条件中
• 验证双目录对比保留在高级操作中
• 验证智能按键的模式切换功能
• 验证所有功能的正常工作
        """)
        layout.addWidget(info_label)
        
        # 创建水平分割器模拟主界面布局
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：筛选条件和高级操作
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 文件筛选条件组（包含基础条件、重复文件、相似文件）
        filter_group = QGroupBox("文件筛选条件")
        filter_layout = QVBoxLayout(filter_group)
        filter_layout.setContentsMargins(8, 8, 8, 8)
        
        self.filter_conditions_widget = FilterConditionsWidget()
        filter_layout.addWidget(self.filter_conditions_widget)
        left_layout.addWidget(filter_group)
        
        # 高级操作组（只包含双目录对比）
        advanced_group = QGroupBox("高级操作")
        advanced_layout = QVBoxLayout(advanced_group)
        advanced_layout.setContentsMargins(8, 8, 8, 8)
        
        self.advanced_filter_widget = AdvancedFilterWidget()
        advanced_layout.addWidget(self.advanced_filter_widget)
        left_layout.addWidget(advanced_group)
        
        # 操作按钮
        self.operation_buttons = OperationButtonsWidget()
        left_layout.addWidget(self.operation_buttons)
        
        main_splitter.addWidget(left_widget)
        
        # 右侧：测试控制和结果显示
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 测试控制区域
        control_group = QGroupBox("测试控制")
        control_layout = QVBoxLayout(control_group)
        
        # 测试按钮
        test_buttons_layout = QHBoxLayout()
        
        test_filter_btn = QPushButton("测试筛选条件")
        test_filter_btn.clicked.connect(self.test_filter_conditions)
        test_buttons_layout.addWidget(test_filter_btn)
        
        test_advanced_btn = QPushButton("测试高级操作")
        test_advanced_btn.clicked.connect(self.test_advanced_operations)
        test_buttons_layout.addWidget(test_advanced_btn)
        
        test_buttons_btn = QPushButton("测试智能按键")
        test_buttons_btn.clicked.connect(self.test_smart_buttons)
        test_buttons_layout.addWidget(test_buttons_btn)
        
        test_all_btn = QPushButton("测试所有功能")
        test_all_btn.clicked.connect(self.test_all_functions)
        test_buttons_layout.addWidget(test_all_btn)
        
        control_layout.addLayout(test_buttons_layout)
        
        # 清空按钮
        clear_layout = QHBoxLayout()
        clear_filter_btn = QPushButton("清空筛选条件")
        clear_filter_btn.clicked.connect(self.filter_conditions_widget.clear)
        clear_layout.addWidget(clear_filter_btn)
        
        clear_advanced_btn = QPushButton("清空高级操作")
        clear_advanced_btn.clicked.connect(self.advanced_filter_widget.clear)
        clear_layout.addWidget(clear_advanced_btn)
        
        clear_all_btn = QPushButton("清空所有设置")
        clear_all_btn.clicked.connect(self.clear_all_settings)
        clear_layout.addWidget(clear_all_btn)
        
        control_layout.addLayout(clear_layout)
        right_layout.addWidget(control_group)
        
        # 结果显示区域
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setMinimumHeight(400)
        result_layout.addWidget(self.result_text)
        
        right_layout.addWidget(result_group)
        
        main_splitter.addWidget(right_widget)
        
        # 设置分割器比例
        main_splitter.setSizes([600, 600])
        layout.addWidget(main_splitter)
        
        # 连接信号
        self.connect_signals()
    
    def connect_signals(self):
        """连接信号"""
        # 连接双目录对比的模式变化信号
        if hasattr(self.advanced_filter_widget, 'dual_dir_check'):
            self.advanced_filter_widget.dual_dir_check.stateChanged.connect(self.on_mode_changed)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(WindowsTheme.get_complete_style())
    
    def test_filter_conditions(self):
        """测试筛选条件功能"""
        self.log("\n=== 测试文件筛选条件 ===")
        
        # 测试基础筛选条件
        extensions = self.filter_conditions_widget.get_extensions()
        pattern = self.filter_conditions_widget.get_pattern()
        min_size, max_size = self.filter_conditions_widget.get_size_range()
        start_date, end_date = self.filter_conditions_widget.get_date_range()
        
        self.log("基础筛选条件:")
        self.log(f"  扩展名: {extensions}")
        self.log(f"  文件名模式: {pattern}")
        self.log(f"  大小范围: {min_size} - {max_size}")
        self.log(f"  时间范围: {start_date} - {end_date}")
        
        # 测试重复文件设置（现在在筛选条件中）
        find_duplicates, min_duplicate_count = self.filter_conditions_widget.get_duplicate_settings()
        self.log(f"\n重复文件筛选:")
        self.log(f"  启用: {find_duplicates}")
        self.log(f"  最少重复: {min_duplicate_count}")
        
        # 测试相似文件设置（现在在筛选条件中）
        find_similar, min_common_chars, similarity_threshold = self.filter_conditions_widget.get_similar_settings()
        self.log(f"\n相似文件筛选:")
        self.log(f"  启用: {find_similar}")
        self.log(f"  相同字符: {min_common_chars}")
        self.log(f"  相似度: {similarity_threshold}%")
        
        self.log("\n✅ 筛选条件功能测试完成")
    
    def test_advanced_operations(self):
        """测试高级操作功能"""
        self.log("\n=== 测试高级操作 ===")
        
        # 测试双目录对比设置
        dual_dir_settings = self.advanced_filter_widget.get_dual_directory_settings()
        
        if dual_dir_settings:
            self.log("双目录对比设置:")
            self.log(f"  启用: {dual_dir_settings.get('enabled', False)}")
            self.log(f"  子目录1: {dual_dir_settings.get('subdir1', '')}")
            self.log(f"  子目录2: {dual_dir_settings.get('subdir2', '')}")
            self.log(f"  匹配模式: {dual_dir_settings.get('match_mode', '')}")
            self.log(f"  时间模式: {dual_dir_settings.get('time_mode', '')}")
        else:
            self.log("双目录对比: 未启用")
        
        self.log("\n✅ 高级操作功能测试完成")
    
    def test_smart_buttons(self):
        """测试智能按键功能"""
        self.log("\n=== 测试智能按键 ===")
        
        # 测试不同模式下的按键文本
        modes = ["normal", "dual_directory", "duplicate"]
        
        for mode in modes:
            self.operation_buttons.set_mode(mode)
            self.log(f"\n{mode}模式:")
            self.log(f"  搜索按钮: {self.operation_buttons.search_btn.text()}")
            self.log(f"  复制按钮: {self.operation_buttons.copy_btn.text()}")
            self.log(f"  移动按钮: {self.operation_buttons.move_btn.text()}")
            self.log(f"  删除按钮: {self.operation_buttons.delete_btn.text()}")
            self.log(f"  模式指示: {self.operation_buttons.mode_label.text()}")
        
        # 恢复普通模式
        self.operation_buttons.set_mode("normal")
        
        self.log("\n✅ 智能按键功能测试完成")
    
    def test_all_functions(self):
        """测试所有功能"""
        self.log("\n=== 综合功能测试 ===")
        
        # 设置一些测试值
        self.filter_conditions_widget.extensions_edit.setText(".txt .doc .pdf")
        self.filter_conditions_widget.pattern_edit.setText("*report*")
        
        if hasattr(self.filter_conditions_widget, 'duplicate_check'):
            self.filter_conditions_widget.duplicate_check.setChecked(True)
            self.filter_conditions_widget.min_duplicate_spin.setValue(3)
        
        if hasattr(self.filter_conditions_widget, 'similar_check'):
            self.filter_conditions_widget.similar_check.setChecked(True)
            self.filter_conditions_widget.min_common_chars_spin.setValue(8)
            self.filter_conditions_widget.similarity_spin.setValue(80)
        
        if hasattr(self.advanced_filter_widget, 'dual_dir_check'):
            self.advanced_filter_widget.dual_dir_check.setChecked(True)
            self.advanced_filter_widget.subdir1_input.setText("dir1")
            self.advanced_filter_widget.subdir2_input.setText("dir2")
        
        # 测试所有功能
        self.test_filter_conditions()
        self.test_advanced_operations()
        self.test_smart_buttons()
        
        self.log("\n🎉 所有功能测试完成！")
        self.log("\n📊 布局优化总结:")
        self.log("  ✅ 重复文件筛选已移到筛选条件中")
        self.log("  ✅ 相似文件筛选已移到筛选条件中")
        self.log("  ✅ 双目录对比保留在高级操作中")
        self.log("  ✅ 智能按键根据模式自动调整")
        self.log("  ✅ 界面布局更加合理和平衡")
    
    def clear_all_settings(self):
        """清空所有设置"""
        self.filter_conditions_widget.clear()
        self.advanced_filter_widget.clear()
        self.log("\n🔄 已清空所有设置")
    
    def on_mode_changed(self, checked):
        """模式变化处理"""
        if checked:
            self.operation_buttons.set_mode("dual_directory")
            self.log("\n🔄 模式切换: 双目录对比模式")
        else:
            self.operation_buttons.set_mode("normal")
            self.log("\n🔄 模式切换: 普通模式")
    
    def log(self, message: str):
        """记录日志"""
        self.result_text.append(message)
        self.result_text.ensureCursorVisible()
        QApplication.processEvents()  # 更新UI

def test_optimized_layout():
    """测试优化布局"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行布局测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🎨 优化布局测试启动")
    print("=" * 60)
    print("✅ 布局调整:")
    print("  • 文件筛选条件：扩展名、模式、大小、时间、重复文件、相似文件")
    print("  • 高级操作：双目录对比")
    print("  • 智能按键：根据模式自动调整功能")
    print()
    print("🎯 测试重点:")
    print("  • 验证重复文件和相似文件筛选已移到筛选条件中")
    print("  • 验证双目录对比保留在高级操作中")
    print("  • 验证智能按键的模式切换功能")
    print("  • 验证所有功能的正常工作")
    print()
    print("💡 使用方法:")
    print("  • 点击各种测试按钮验证功能")
    print("  • 勾选双目录对比观察模式切换")
    print("  • 设置各种筛选条件测试获取功能")
    print("  • 使用清空按钮重置设置")
    print()
    
    # 创建测试窗口
    test_widget = OptimizedLayoutTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行布局测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_optimized_layout()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
