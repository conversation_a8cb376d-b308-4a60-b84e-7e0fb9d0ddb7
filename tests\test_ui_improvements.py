#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI改进测试
测试复选框✓符号显示和控件宽度优化
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PySide6.QtWidgets import (
        QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
        QPushButton, QLabel, QGroupBox, QScrollArea
    )
    from PySide6.QtCore import QTimer
    from file_filter_tool.ui.components import (
        CustomCheckBox, AdvancedFilterWidget, NumberedFileFilterWidget,
        FilterConditionsWidget, OperationButtonsWidget
    )
    from file_filter_tool.ui.themes import WindowsTheme, VSTheme
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    print("PySide6 not available")

class UIImprovementsTestWidget(QWidget):
    """UI改进测试控件"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "windows"
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("UI改进测试 - 复选框✓符号 & 控件宽度优化")
        self.setMinimumSize(800, 700)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        
        # 说明文字
        info_label = QLabel("""
🔧 UI改进测试

本测试验证以下改进：
✅ 复选框✓符号正确显示
📏 控件宽度合理优化
🎨 主题切换正常工作
📱 界面缩放适配良好

测试内容：
1. 复选框选中时显示白色✓符号
2. 各种控件宽度设置合理
3. 主题切换时样式正确更新
4. 界面在不同缩放下正常显示
        """)
        layout.addWidget(info_label)
        
        # 主题切换按钮
        theme_layout = QHBoxLayout()
        self.theme_btn = QPushButton("切换到 VS 主题")
        self.theme_btn.clicked.connect(self.switch_theme)
        theme_layout.addWidget(self.theme_btn)
        theme_layout.addStretch()
        layout.addLayout(theme_layout)
        
        # 测试组1：基本复选框
        basic_group = QGroupBox("基本复选框测试")
        basic_layout = QVBoxLayout(basic_group)
        
        self.basic_checkboxes = []
        for i in range(5):
            checkbox = CustomCheckBox(f"测试选项 {i+1}")
            if i % 2 == 1:  # 奇数项默认选中
                checkbox.setChecked(True)
            basic_layout.addWidget(checkbox)
            self.basic_checkboxes.append(checkbox)
        
        layout.addWidget(basic_group)
        
        # 测试组2：筛选条件组件
        conditions_group = QGroupBox("筛选条件组件")
        conditions_layout = QVBoxLayout(conditions_group)
        
        self.filter_conditions = FilterConditionsWidget()
        conditions_layout.addWidget(self.filter_conditions)
        
        layout.addWidget(conditions_group)
        
        # 测试组3：高级筛选组件
        advanced_group = QGroupBox("高级筛选组件")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.advanced_filter = AdvancedFilterWidget()
        advanced_layout.addWidget(self.advanced_filter)
        
        layout.addWidget(advanced_group)
        
        # 测试组4：编号文件筛选组件
        numbered_group = QGroupBox("编号文件筛选组件")
        numbered_layout = QVBoxLayout(numbered_group)
        
        self.numbered_filter = NumberedFileFilterWidget()
        numbered_layout.addWidget(self.numbered_filter)
        
        layout.addWidget(numbered_group)
        
        # 测试组5：操作按钮组件
        buttons_group = QGroupBox("操作按钮组件")
        buttons_layout = QVBoxLayout(buttons_group)
        
        self.operation_buttons = OperationButtonsWidget()
        buttons_layout.addWidget(self.operation_buttons)
        
        layout.addWidget(buttons_group)
        
        # 测试控制按钮
        control_layout = QHBoxLayout()
        
        check_all_btn = QPushButton("全选复选框")
        check_all_btn.clicked.connect(self.check_all)
        control_layout.addWidget(check_all_btn)
        
        uncheck_all_btn = QPushButton("全不选复选框")
        uncheck_all_btn.clicked.connect(self.uncheck_all)
        control_layout.addWidget(uncheck_all_btn)
        
        test_values_btn = QPushButton("填充测试数据")
        test_values_btn.clicked.connect(self.fill_test_data)
        control_layout.addWidget(test_values_btn)
        
        clear_btn = QPushButton("清空所有数据")
        clear_btn.clicked.connect(self.clear_all)
        control_layout.addWidget(clear_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 设置滚动区域
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)
        
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll)
    
    def apply_theme(self):
        """应用主题"""
        if self.current_theme == "windows":
            self.setStyleSheet(WindowsTheme.get_complete_style())
            # 更新所有复选框样式
            for checkbox in self.get_all_checkboxes():
                checkbox.apply_windows_style()
        else:
            self.setStyleSheet(VSTheme.get_complete_style())
            # 更新所有复选框样式
            for checkbox in self.get_all_checkboxes():
                checkbox.apply_vs_style()
    
    def get_all_checkboxes(self):
        """获取所有复选框"""
        checkboxes = []
        checkboxes.extend(self.basic_checkboxes)
        
        # 从高级筛选组件获取复选框
        if hasattr(self.advanced_filter, 'duplicate_check'):
            checkboxes.append(self.advanced_filter.duplicate_check)
        if hasattr(self.advanced_filter, 'similar_check'):
            checkboxes.append(self.advanced_filter.similar_check)
        
        # 从编号文件筛选组件获取复选框
        if hasattr(self.numbered_filter, 'enable_check'):
            checkboxes.append(self.numbered_filter.enable_check)
        if hasattr(self.numbered_filter, 'ignore_extension_check'):
            checkboxes.append(self.numbered_filter.ignore_extension_check)
        if hasattr(self.numbered_filter, 'case_sensitive_check'):
            checkboxes.append(self.numbered_filter.case_sensitive_check)
        
        return checkboxes
    
    def switch_theme(self):
        """切换主题"""
        if self.current_theme == "windows":
            self.current_theme = "vs"
            self.theme_btn.setText("切换到 Windows 主题")
            print("🌙 切换到 Visual Studio 深色主题")
        else:
            self.current_theme = "windows"
            self.theme_btn.setText("切换到 VS 主题")
            print("☀️ 切换到 Windows 浅色主题")
        
        self.apply_theme()
    
    def check_all(self):
        """全选复选框"""
        for checkbox in self.get_all_checkboxes():
            if checkbox.isEnabled():
                checkbox.setChecked(True)
        print("✅ 全选复选框完成")
    
    def uncheck_all(self):
        """全不选复选框"""
        for checkbox in self.get_all_checkboxes():
            if checkbox.isEnabled():
                checkbox.setChecked(False)
        print("❌ 全不选复选框完成")
    
    def fill_test_data(self):
        """填充测试数据"""
        # 筛选条件
        self.filter_conditions.extensions_edit.setText(".txt .py .jpg")
        self.filter_conditions.pattern_edit.setText("test.*")
        self.filter_conditions.min_size_edit.setText("1KB")
        self.filter_conditions.max_size_edit.setText("10MB")
        self.filter_conditions.days_spin.setValue(30)
        
        # 高级筛选
        self.advanced_filter.duplicate_check.setChecked(True)
        self.advanced_filter.min_duplicate_spin.setValue(3)
        self.advanced_filter.similar_check.setChecked(True)
        self.advanced_filter.min_common_chars_spin.setValue(8)
        self.advanced_filter.similarity_spin.setValue(85)
        
        # 编号文件筛选
        self.numbered_filter.enable_check.setChecked(True)
        self.numbered_filter.strategy_combo.setCurrentText("保留最新N个")
        self.numbered_filter.count_spin.setValue(3)
        self.numbered_filter.interval_spin.setValue(14)
        self.numbered_filter.ignore_extension_check.setChecked(True)
        self.numbered_filter.case_sensitive_check.setChecked(False)
        
        print("📝 测试数据填充完成")
    
    def clear_all(self):
        """清空所有数据"""
        self.filter_conditions.clear()
        self.advanced_filter.clear()
        self.numbered_filter.clear()
        
        for checkbox in self.basic_checkboxes:
            checkbox.setChecked(False)
        
        print("🧹 所有数据清空完成")

def test_ui_improvements():
    """测试UI改进"""
    if not PYSIDE6_AVAILABLE:
        print("❌ PySide6不可用，无法进行UI测试")
        return False
    
    app = QApplication(sys.argv)
    
    print("🔧 UI改进测试启动")
    print("=" * 50)
    print("📋 测试内容:")
    print("  • 复选框✓符号显示修复")
    print("  • 控件宽度优化")
    print("  • 主题切换功能")
    print("  • 界面缩放适配")
    print()
    print("💡 测试重点:")
    print("  • 选中复选框时应显示白色✓符号")
    print("  • 控件宽度应该合理，不会过宽或过窄")
    print("  • 主题切换时所有样式应正确更新")
    print("  • 界面应该适应不同的系统缩放")
    print()
    print("🎯 验证方法:")
    print("  • 点击复选框观察✓符号")
    print("  • 切换主题观察样式变化")
    print("  • 调整窗口大小观察控件布局")
    print("  • 填充测试数据观察控件宽度")
    print()
    
    # 创建测试窗口
    test_widget = UIImprovementsTestWidget()
    test_widget.show()
    
    return app.exec()

def main():
    """主函数"""
    if not PYSIDE6_AVAILABLE:
        print("❌ 需要安装PySide6才能进行UI测试")
        print("请运行: pip install PySide6")
        return False
    
    try:
        result = test_ui_improvements()
        return result == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
