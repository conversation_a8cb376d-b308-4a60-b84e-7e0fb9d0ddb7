#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件筛选工具 - 简化版GUI启动器
"""

import sys
import os

def main():
    """主函数"""
    print("🎯 文件筛选过滤工具")
    print("=" * 30)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        input("按回车键退出...")
        return
    
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}")
    
    # 尝试启动PySide6版本
    print("\n🚀 尝试启动PySide6版本...")
    try:
        import gui_pyside6
        if hasattr(gui_pyside6, 'PYSIDE6_AVAILABLE') and gui_pyside6.PYSIDE6_AVAILABLE:
            print("✅ PySide6可用，启动现代化界面...")
            gui_pyside6.main()
            return
        else:
            print("❌ PySide6不可用")
    except Exception as e:
        print(f"❌ PySide6启动失败: {e}")
    
    # 尝试启动tkinter版本
    print("\n🚀 尝试启动tkinter版本...")
    try:
        import gui
        print("✅ tkinter可用，启动传统界面...")
        gui.main()
        return
    except Exception as e:
        print(f"❌ tkinter启动失败: {e}")
    
    # 如果都失败了
    print("\n❌ 无法启动图形界面")
    print("\n💡 解决方案:")
    print("1. 安装PySide6: pip install PySide6")
    print("2. 使用命令行版本: python main.py --help")
    print("3. 运行依赖检查: python 检查依赖.py")
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序异常: {e}")
        input("按回车键退出...")
